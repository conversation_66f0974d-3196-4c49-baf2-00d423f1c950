/**
 * Rule Form Editor Component - V1 Compatible
 * Based on V1 RulesDialog form implementation
 */

import React, { useState, useEffect } from 'react'
import {
  Box,
  Typography,
  Grid,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  FormHelperText,
  FormControlLabel,
  Switch,
  Button,
  Paper,
  Alert,
  Divider,
  IconButton,
  Tooltip,
  Checkbox
} from '@mui/material'
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Flag as FlagIcon,
  Save as SaveIcon,
  Link as LinkIcon,
  LinkOff as LinkOffIcon
} from '@mui/icons-material'

import {
  Rule,
  RuleType,
  RulePriority,
  RuleCondition,
  priorityConfig,
  RuleFormValues,
  initialFormValues,
  initialLayerGroupFormValues,
  initialTraitGroupFormValues
} from '../../../shared/types/Rules.types'
import { validateRule, generateRuleId } from '../../../shared/utils/rulesUtils'
import { useAppStore } from '../../../application/stores/appStore'

interface RuleFormEditorProps {
  rule?: Rule | null
  ruleType: RuleType
  onSave: (rule: Rule) => void
  onCancel: () => void
}



export const RuleFormEditor: React.FC<RuleFormEditorProps> = ({
  rule,
  ruleType,
  onSave,
  onCancel
}) => {
  // Get real layers and traits from app store
  const { currentProject } = useAppStore()
  const allLayers = currentProject?.layers || []

  // Sort layers by order (same as layers panel)
  const layers = [...allLayers].sort((a, b) => (a.order || 0) - (b.order || 0))
  // Form state
  const [formValues, setFormValues] = useState<RuleFormValues>(() => {
    if (rule) {
      return {
        id: rule.id,
        name: rule.name,
        description: rule.description || '',
        type: rule.type,
        conditions: rule.conditions,
        order: rule.order,
        priority: rule.priority,
        groupLayers: rule.groupLayers || [],
        groupTraits: rule.groupTraits || [],
        groupBehavior: rule.groupBehavior || 'sync',
        enabled: rule.enabled
      }
    }

    switch (ruleType) {
      case 'LAYER_GROUP':
        return initialLayerGroupFormValues
      case 'TRAIT_GROUP':
        return initialTraitGroupFormValues
      default:
        return initialFormValues
    }
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [validationWarnings, setValidationWarnings] = useState<string[]>([])

  // Separate IF and THEN conditions for easier management
  const [ifConditions, setIfConditions] = useState<RuleCondition[]>(() => {
    if (rule && rule.type === 'IF_THEN') {
      return rule.conditions.filter(c => c.layerId && !c.targetLayerId)
    }
    return [{ layerId: null, traitId: null }]
  })

  const [thenConditions, setThenConditions] = useState<RuleCondition[]>(() => {
    if (rule && rule.type === 'IF_THEN') {
      return rule.conditions.filter(c => c.targetLayerId)
    }
    return [{ targetLayerId: null, targetTraitId: null, operator: 'MUST_HAVE' }]
  })

  const [groupLayers, setGroupLayers] = useState<string[]>(() => {
    return rule?.groupLayers || []
  })

  const [groupTraits, setGroupTraits] = useState<{layerId: string, traitIds: string[]}[]>(() => {
    return rule?.groupTraits || []
  })

  // Chain functionality state - separate for each component
  const [ifLayerChainLocked, setIfLayerChainLocked] = useState(false)
  const [ifOperatorChainLocked, setIfOperatorChainLocked] = useState(false)
  const [thenLayerChainLocked, setThenLayerChainLocked] = useState(false)
  const [thenOperatorChainLocked, setThenOperatorChainLocked] = useState(false)
  const [thenLogicalChainLocked, setThenLogicalChainLocked] = useState(false)

  // Helper functions
  const getTraitsForLayer = (layerId: string | null | undefined) => {
    if (!layerId) return []

    // Check if layers are available
    if (!layers || !Array.isArray(layers)) {
      console.warn('getTraitsForLayer: layers is not available or not an array')
      return []
    }

    const layer = layers.find(l => l.id === layerId)
    if (!layer) {
      console.warn(`getTraitsForLayer: layer with ID ${layerId} not found`)
      return []
    }

    if (!layer.traits || !Array.isArray(layer.traits)) {
      console.warn(`getTraitsForLayer: traits for layer ${layer.name} (${layerId}) are not available or not an array`)
      return []
    }

    return layer.traits.map(trait => ({
      id: trait.id,
      name: trait.name
    }))
  }

  // Get layers that are above the IF condition layer (for THEN conditions)
  const getLayersAboveIfCondition = () => {
    // For now, show all layers in THEN conditions
    // TODO: Implement proper layer hierarchy filtering when needed
    return layers
  }

  const handleFormChange = (field: keyof RuleFormValues, value: any) => {
    setFormValues(prev => ({
      ...prev,
      [field]: value
    }))

    // Clear related errors
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  // Chain functionality handlers - separate for each component
  const handleToggleIfLayerChain = () => {
    const newLockState = !ifLayerChainLocked
    setIfLayerChainLocked(newLockState)

    // If chain is being activated, sync all existing conditions to first condition
    if (newLockState && ifConditions.length > 1) {
      setIfConditions(prev => copyFirstConditionToAll(prev, 'layerId'))
    }
  }

  const handleToggleIfOperatorChain = () => {
    const newLockState = !ifOperatorChainLocked
    setIfOperatorChainLocked(newLockState)

    // If chain is being activated, sync all existing conditions to first condition
    if (newLockState && ifConditions.length > 1) {
      setIfConditions(prev => copyFirstConditionToAll(prev, 'logicalOperator'))
    }
  }

  const handleToggleThenLayerChain = () => {
    const newLockState = !thenLayerChainLocked
    setThenLayerChainLocked(newLockState)

    // If chain is being activated, sync all existing conditions to first condition
    if (newLockState && thenConditions.length > 1) {
      setThenConditions(prev => copyFirstConditionToAll(prev, 'targetLayerId'))
    }
  }

  const handleToggleThenOperatorChain = () => {
    const newLockState = !thenOperatorChainLocked
    setThenOperatorChainLocked(newLockState)

    // If chain is being activated, sync all existing conditions to first condition
    if (newLockState && thenConditions.length > 1) {
      setThenConditions(prev => copyFirstConditionToAll(prev, 'operator'))
    }
  }

  const handleToggleThenLogicalChain = () => {
    const newLockState = !thenLogicalChainLocked
    setThenLogicalChainLocked(newLockState)

    // If chain is being activated, sync all existing conditions to first condition
    if (newLockState && thenConditions.length > 1) {
      setThenConditions(prev => copyFirstConditionToAll(prev, 'logicalOperator'))
    }
  }

  const copyFirstConditionToAll = (conditions: RuleCondition[], field: string) => {
    if (conditions.length === 0) return conditions

    const firstCondition = conditions[0]
    return conditions.map((condition, index) => {
      if (index === 0) return condition

      // Copy only the specified field from first condition
      return {
        ...condition,
        [field]: firstCondition[field as keyof RuleCondition]
      }
    })
  }

  // IF Condition handlers
  const handleAddIfCondition = () => {
    const newCondition = {
      layerId: ifLayerChainLocked && ifConditions.length > 0 ? ifConditions[0].layerId : null,
      traitId: null, // Always independent
      logicalOperator: ifOperatorChainLocked && ifConditions.length > 0 ? ifConditions[0].logicalOperator : 'AND' as const
    }

    setIfConditions(prev => [...prev, newCondition])
  }

  const handleIfConditionChange = (index: number, field: keyof RuleCondition, value: any) => {
    setIfConditions(prev => {
      const newConditions = [...prev]
      newConditions[index] = { ...newConditions[index], [field]: value }

      // Apply chain logic based on which field changed and which chains are active
      if (index === 0) {
        if (field === 'layerId' && ifLayerChainLocked) {
          return copyFirstConditionToAll(newConditions, 'layerId')
        }
        if (field === 'logicalOperator' && ifOperatorChainLocked) {
          return copyFirstConditionToAll(newConditions, 'logicalOperator')
        }
      }

      return newConditions
    })
  }

  const handleDeleteIfCondition = (index: number) => {
    if (ifConditions.length > 1) {
      setIfConditions(prev => prev.filter((_, i) => i !== index))
    }
  }

  // THEN Condition handlers
  const handleAddThenCondition = () => {
    const newCondition = {
      targetLayerId: thenLayerChainLocked && thenConditions.length > 0 ? thenConditions[0].targetLayerId : null,
      targetTraitId: null, // Always independent
      operator: thenOperatorChainLocked && thenConditions.length > 0 ? thenConditions[0].operator : 'MUST_HAVE',
      logicalOperator: thenLogicalChainLocked && thenConditions.length > 0 ? thenConditions[0].logicalOperator : 'AND' as const
    }

    setThenConditions(prev => [...prev, newCondition])
  }

  const handleThenConditionChange = (index: number, field: keyof RuleCondition, value: any) => {
    setThenConditions(prev => {
      const newConditions = [...prev]
      newConditions[index] = { ...newConditions[index], [field]: value }

      // Apply chain logic based on which field changed and which chains are active
      if (index === 0) {
        if (field === 'targetLayerId' && thenLayerChainLocked) {
          return copyFirstConditionToAll(newConditions, 'targetLayerId')
        }
        if (field === 'operator' && thenOperatorChainLocked) {
          return copyFirstConditionToAll(newConditions, 'operator')
        }
        if (field === 'logicalOperator' && thenLogicalChainLocked) {
          return copyFirstConditionToAll(newConditions, 'logicalOperator')
        }
      }

      return newConditions
    })
  }

  const handleDeleteThenCondition = (index: number) => {
    if (thenConditions.length > 1) {
      setThenConditions(prev => prev.filter((_, i) => i !== index))
    }
  }

  // Layer group handlers
  const handleToggleLayerInGroup = (layerId: string) => {
    setGroupLayers(prev => {
      if (prev.includes(layerId)) {
        return prev.filter(id => id !== layerId)
      } else {
        return [...prev, layerId]
      }
    })
  }

  // Validation
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}
    const warnings: string[] = []

    if (!formValues.name.trim()) {
      newErrors.name = 'Rule name is required'
    }

    if (ruleType === 'IF_THEN') {
      // IF conditions require both layer and trait (no "Any Trait" for IF)
      const validIfConditions = ifConditions.filter(c => c.layerId && c.traitId)
      // THEN conditions only require layer (targetTraitId can be empty for "Any Trait")
      const validThenConditions = thenConditions.filter(c => c.targetLayerId)

      if (validIfConditions.length === 0) {
        newErrors.ifConditions = 'At least one IF condition is required'
      }

      if (validThenConditions.length === 0) {
        newErrors.thenConditions = 'At least one THEN condition is required'
      }
    } else if (ruleType === 'LAYER_GROUP') {
      if (groupLayers.length < 2) {
        newErrors.groupLayers = 'At least 2 layers are required for grouping'
      }
    } else if (ruleType === 'TRAIT_GROUP') {
      if (groupTraits.length === 0) {
        newErrors.groupTraits = 'At least one trait group is required'
      }
    }

    setErrors(newErrors)
    setValidationWarnings(warnings)

    return Object.keys(newErrors).length === 0
  }

  // Form submission
  const handleSubmit = () => {
    if (!validateForm()) return

    // Build the rule object
    const ruleData: Rule = {
      id: formValues.id || generateRuleId(),
      name: formValues.name,
      description: formValues.description,
      type: ruleType,
      conditions: ruleType === 'IF_THEN' ? [
        ...ifConditions.map(condition => ({
          ...condition,
          type: 'IF' as const,
          // Ensure logicalOperator is preserved
          logicalOperator: condition.logicalOperator || (ifConditions.indexOf(condition) === 0 ? undefined : 'AND')
        })),
        ...thenConditions.map(condition => ({ ...condition, type: 'THEN' as const }))
      ] : [],
      order: formValues.order,
      priority: formValues.priority || 'medium',
      groupLayers: ruleType === 'LAYER_GROUP' ? groupLayers : undefined,
      groupTraits: ruleType === 'TRAIT_GROUP' ? groupTraits : undefined,
      groupBehavior: ruleType !== 'IF_THEN' ? formValues.groupBehavior : undefined,
      enabled: formValues.enabled !== false
    }

    onSave(ruleData)
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          {rule
            ? `Edit ${ruleType === 'IF_THEN' ? 'Trait Rule' : ruleType === 'LAYER_GROUP' ? 'Layer Group Rule' : 'Trait Group Rule'}`
            : `Create New ${ruleType === 'IF_THEN' ? 'Trait Rule' : ruleType === 'LAYER_GROUP' ? 'Layer Group Rule' : 'Trait Group Rule'}`}
        </Typography>
      </Box>

      {/* Validation Warnings */}
      {validationWarnings.length > 0 && (
        <Alert severity="warning" sx={{ mb: 2 }} variant="outlined">
          <Typography variant="subtitle2">Warnings:</Typography>
          <ul style={{ marginTop: 4, marginBottom: 0, paddingLeft: 20 }}>
            {validationWarnings.map((warning, index) => (
              <li key={index}>
                <Typography variant="body2">{warning}</Typography>
              </li>
            ))}
          </ul>
        </Alert>
      )}

      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Box sx={{ display: 'flex', gap: 2, flexDirection: { xs: 'column', sm: 'row' } }}>
          <TextField
            fullWidth
            label="Rule Name"
            value={formValues.name}
            onChange={(e) => handleFormChange('name', e.target.value)}
            error={!!errors.name}
            helperText={errors.name}
          />
          <TextField
            fullWidth
            label="Description (optional)"
            value={formValues.description}
            onChange={(e) => handleFormChange('description', e.target.value)}
          />
        </Box>

        {/* Priority and Enabled settings */}
        <Box sx={{ display: 'flex', gap: 2, flexDirection: { xs: 'column', sm: 'row' } }}>
          <FormControl fullWidth size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Priority</InputLabel>
            <Select
              value={formValues.priority || 'medium'}
              label="Priority"
              onChange={(e) => handleFormChange('priority', e.target.value)}
            >
              <MenuItem value="highest">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <FlagIcon color="error" fontSize="small" />
                  <Typography>Highest Priority (1)</Typography>
                </Box>
              </MenuItem>
              <MenuItem value="high">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <FlagIcon color="warning" fontSize="small" />
                  <Typography>High Priority (10)</Typography>
                </Box>
              </MenuItem>
              <MenuItem value="medium">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <FlagIcon color="primary" fontSize="small" />
                  <Typography>Medium Priority (50)</Typography>
                </Box>
              </MenuItem>
              <MenuItem value="low">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <FlagIcon color="info" fontSize="small" />
                  <Typography>Low Priority (100)</Typography>
                </Box>
              </MenuItem>
              <MenuItem value="lowest">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <FlagIcon color="disabled" fontSize="small" />
                  <Typography>Lowest Priority (500)</Typography>
                </Box>
              </MenuItem>
            </Select>
            <FormHelperText>Higher priority rules are evaluated first</FormHelperText>
          </FormControl>

          <Box>
            <FormControlLabel
              control={
                <Switch
                  checked={formValues.enabled !== false}
                  onChange={(e) => handleFormChange('enabled', e.target.checked)}
                  color="primary"
                />
              }
              label="Rule Enabled"
            />
            <FormHelperText>Disabled rules are ignored during rule evaluation</FormHelperText>
          </Box>
        </Box>
      </Box>

      {/* Rule Type Specific Forms */}
      {ruleType === 'IF_THEN' ? (
        // Trait Rules Form
        <Box sx={{ mt: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Rule Conditions
          </Typography>

          {/* Display errors */}
          {(errors.ifConditions || errors.thenConditions) && (
            <FormHelperText error sx={{ ml: 1 }}>
              {errors.ifConditions || errors.thenConditions}
            </FormHelperText>
          )}

          <Box sx={{ backgroundColor: 'background.default', p: 2, borderRadius: 1, mt: 2 }}>
            {/* IF Conditions Section */}
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="subtitle2">IF Conditions:</Typography>
              <Button
                variant="outlined"
                size="small"
                startIcon={<AddIcon />}
                onClick={handleAddIfCondition}
              >
                Add Condition
              </Button>
            </Box>

            {/* Display IF conditions */}
            {ifConditions.length > 0 ? (
              <Box>
                {ifConditions.map((condition, index) => (
                  <React.Fragment key={`if-${index}`}>
                    <Paper elevation={1} sx={{ p: 2, mb: 1, borderRadius: 1 }}>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        {/* Layer and Trait Row */}
                        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
                          {/* Layer Input + Chain Icon (200px + 44px) */}
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '244px' }}>
                            <FormControl size="small" sx={{ width: '200px' }}>
                              <InputLabel>Layer</InputLabel>
                              <Select
                                value={condition.layerId || ''}
                                label="Layer"
                                onChange={(e) => handleIfConditionChange(index, 'layerId', e.target.value || null)}
                              >
                                <MenuItem value=""><em>Select a Layer</em></MenuItem>
                                {layers.map((layer) => (
                                  <MenuItem key={layer.id} value={layer.id}>
                                    {layer.name}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                            {/* Chain Icon Space - always 44px */}
                            <Box sx={{ width: '44px', display: 'flex', justifyContent: 'center' }}>
                              {index === 0 && ifConditions.length > 1 && (
                                <Tooltip title={ifLayerChainLocked ? "Layer chain active - Layer selections are synchronized" : "Layer chain inactive - Independent layer selections"}>
                                  <IconButton
                                    size="small"
                                    onClick={handleToggleIfLayerChain}
                                    sx={{
                                      color: ifLayerChainLocked ? 'primary.main' : 'text.secondary',
                                      '&:hover': { bgcolor: 'action.hover' },
                                      width: '40px',
                                      height: '40px'
                                    }}
                                  >
                                    <LinkIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              )}
                            </Box>
                          </Box>

                          {/* Trait Input + Chain Icon (200px + 44px) */}
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '244px' }}>
                            <FormControl size="small" disabled={!condition.layerId} sx={{ width: '200px' }}>
                              <InputLabel>Trait</InputLabel>
                              <Select
                                value={condition.traitId || ''}
                                label="Trait"
                                onChange={(e) => handleIfConditionChange(index, 'traitId', e.target.value || undefined)}
                              >
                                <MenuItem value=""><em>Any Trait</em></MenuItem>
                                {getTraitsForLayer(condition.layerId).map((trait) => (
                                  <MenuItem key={trait.id} value={trait.id}>
                                    {trait.name}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                            {/* Chain Icon Space - always 44px (empty for trait inputs) */}
                            <Box sx={{ width: '44px' }} />
                          </Box>

                          {/* AND/OR Logical Operator - only show when there are multiple conditions and not the last one */}
                          {ifConditions.length > 1 && index < ifConditions.length - 1 && (
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '144px' }}>
                              <Paper elevation={2} sx={{ display: 'inline-flex', overflow: 'hidden', border: 1, borderColor: 'divider', borderRadius: 1, width: '100px' }}>
                                <Button
                                  variant={condition.logicalOperator === 'AND' ? "contained" : "outlined"}
                                  size="small"
                                  sx={{ borderRadius: 0, minWidth: '50px', borderRight: 1, borderColor: 'divider' }}
                                  onClick={() => handleIfConditionChange(index, 'logicalOperator', 'AND')}
                                >
                                  AND
                                </Button>
                                <Button
                                  variant={condition.logicalOperator === 'OR' ? "contained" : "outlined"}
                                  size="small"
                                  sx={{ borderRadius: 0, minWidth: '50px' }}
                                  onClick={() => handleIfConditionChange(index, 'logicalOperator', 'OR')}
                                >
                                  OR
                                </Button>
                              </Paper>
                              {/* Logical Chain Icon Space - always 44px */}
                              <Box sx={{ width: '44px', display: 'flex', justifyContent: 'center' }}>
                                {index === 0 && (
                                  <Tooltip title={ifOperatorChainLocked ? "Logical chain active - AND/OR selections are synchronized" : "Logical chain inactive - Independent AND/OR selections"}>
                                    <IconButton
                                      size="small"
                                      onClick={handleToggleIfOperatorChain}
                                      sx={{
                                        color: ifOperatorChainLocked ? 'primary.main' : 'text.secondary',
                                        '&:hover': { bgcolor: 'action.hover' },
                                        width: '40px',
                                        height: '40px'
                                      }}
                                    >
                                      <LinkIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                )}
                              </Box>
                            </Box>
                          )}

                          {/* Delete Button */}
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {ifConditions.length > 1 && (
                              <Tooltip title="Remove condition">
                                <IconButton color="error" size="small" onClick={() => handleDeleteIfCondition(index)}>
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                          </Box>
                        </Box>
                      </Box>
                    </Paper>
                  </React.Fragment>
                ))}
              </Box>
            ) : (
              <Typography color="text.secondary" sx={{ mb: 3 }}>
                No IF conditions defined. Click the + button to add.
              </Typography>
            )}

            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 2 }}>
              <Divider sx={{ flex: 1 }} />
              <Typography variant="body2" sx={{ mx: 2 }}>THEN</Typography>
              <Divider sx={{ flex: 1 }} />
            </Box>

            {/* THEN Conditions Section */}
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="subtitle2">THEN Conditions:</Typography>
              <Button
                variant="outlined"
                size="small"
                startIcon={<AddIcon />}
                onClick={handleAddThenCondition}
              >
                Add Condition
              </Button>
            </Box>

            {/* Display THEN conditions */}
            {thenConditions.length > 0 ? (
              <Box>
                {thenConditions.map((condition, index) => (
                  <React.Fragment key={`then-${index}`}>
                    <Paper elevation={1} sx={{ p: 2, mb: 1, borderRadius: 1 }}>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        {/* Main Row: Target Layer, Operator, Target Trait */}
                        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
                          {/* Target Layer Input + Chain Icon (200px + 44px) */}
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '244px' }}>
                            <FormControl size="small" sx={{ width: '200px' }}>
                              <InputLabel>Target Layer</InputLabel>
                              <Select
                                value={condition.targetLayerId || ''}
                                label="Target Layer"
                                onChange={(e) => handleThenConditionChange(index, 'targetLayerId', e.target.value || null)}
                              >
                                <MenuItem value=""><em>Select a Layer</em></MenuItem>
                                {getLayersAboveIfCondition().map((layer) => (
                                  <MenuItem key={layer.id} value={layer.id}>
                                    {layer.name}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                            {/* Chain Icon Space - always 44px */}
                            <Box sx={{ width: '44px', display: 'flex', justifyContent: 'center' }}>
                              {index === 0 && thenConditions.length > 1 && (
                                <Tooltip title={thenLayerChainLocked ? "Layer chain active - Layer selections are synchronized" : "Layer chain inactive - Independent layer selections"}>
                                  <IconButton
                                    size="small"
                                    onClick={handleToggleThenLayerChain}
                                    sx={{
                                      color: thenLayerChainLocked ? 'primary.main' : 'text.secondary',
                                      '&:hover': { bgcolor: 'action.hover' },
                                      width: '40px',
                                      height: '40px'
                                    }}
                                  >
                                    <LinkIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              )}
                            </Box>
                          </Box>

                          {/* Operator Input + Chain Icon (200px + 44px) */}
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '244px' }}>
                            <FormControl size="small" sx={{ width: '200px' }}>
                              <InputLabel>Operator</InputLabel>
                              <Select
                                value={condition.operator || 'MUST_HAVE'}
                                label="Operator"
                                onChange={(e) => handleThenConditionChange(index, 'operator', e.target.value)}
                              >
                                <MenuItem value="MUST_HAVE">Must Have</MenuItem>
                                <MenuItem value="CANNOT_HAVE">Cannot Have</MenuItem>
                              </Select>
                            </FormControl>
                            {/* Chain Icon Space - always 44px */}
                            <Box sx={{ width: '44px', display: 'flex', justifyContent: 'center' }}>
                              {index === 0 && thenConditions.length > 1 && (
                                <Tooltip title={thenOperatorChainLocked ? "Operator chain active - Operator selections are synchronized" : "Operator chain inactive - Independent operator selections"}>
                                  <IconButton
                                    size="small"
                                    onClick={handleToggleThenOperatorChain}
                                    sx={{
                                      color: thenOperatorChainLocked ? 'primary.main' : 'text.secondary',
                                      '&:hover': { bgcolor: 'action.hover' },
                                      width: '40px',
                                      height: '40px'
                                    }}
                                  >
                                    <LinkIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              )}
                            </Box>
                          </Box>

                          {/* Target Trait Input + Chain Icon (200px + 44px) */}
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '244px' }}>
                            <FormControl size="small" disabled={!condition.targetLayerId} sx={{ width: '200px' }}>
                              <InputLabel>Target Trait</InputLabel>
                              <Select
                                value={condition.targetTraitId || ''}
                                label="Target Trait"
                                onChange={(e) => handleThenConditionChange(index, 'targetTraitId', e.target.value || undefined)}
                              >
                                <MenuItem value=""><em>Any Trait</em></MenuItem>
                                {getTraitsForLayer(condition.targetLayerId).map((trait) => (
                                  <MenuItem key={trait.id} value={trait.id}>
                                    {trait.name}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                            {/* Chain Icon Space - always 44px (empty for trait inputs) */}
                            <Box sx={{ width: '44px' }} />
                          </Box>

                          {/* AND/OR Logical Operator - only show when there are multiple conditions and not the last one */}
                          {thenConditions.length > 1 && index < thenConditions.length - 1 && (
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '144px' }}>
                              <Paper elevation={2} sx={{ display: 'inline-flex', overflow: 'hidden', border: 1, borderColor: 'divider', borderRadius: 1, width: '100px' }}>
                                <Button
                                  variant={condition.logicalOperator === 'AND' ? "contained" : "outlined"}
                                  size="small"
                                  sx={{ borderRadius: 0, minWidth: '50px', borderRight: 1, borderColor: 'divider' }}
                                  onClick={() => handleThenConditionChange(index, 'logicalOperator', 'AND')}
                                >
                                  AND
                                </Button>
                                <Button
                                  variant={condition.logicalOperator === 'OR' ? "contained" : "outlined"}
                                  size="small"
                                  sx={{ borderRadius: 0, minWidth: '50px' }}
                                  onClick={() => handleThenConditionChange(index, 'logicalOperator', 'OR')}
                                >
                                  OR
                                </Button>
                              </Paper>
                              {/* Logical Chain Icon Space - always 44px */}
                              <Box sx={{ width: '44px', display: 'flex', justifyContent: 'center' }}>
                                {index === 0 && (
                                  <Tooltip title={thenLogicalChainLocked ? "Logical chain active - AND/OR selections are synchronized" : "Logical chain inactive - Independent AND/OR selections"}>
                                    <IconButton
                                      size="small"
                                      onClick={handleToggleThenLogicalChain}
                                      sx={{
                                        color: thenLogicalChainLocked ? 'primary.main' : 'text.secondary',
                                        '&:hover': { bgcolor: 'action.hover' },
                                        width: '40px',
                                        height: '40px'
                                      }}
                                    >
                                      <LinkIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                )}
                              </Box>
                            </Box>
                          )}

                          {/* Delete Button */}
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {thenConditions.length > 1 && (
                              <Tooltip title="Remove condition">
                                <IconButton color="error" size="small" onClick={() => handleDeleteThenCondition(index)}>
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                          </Box>
                        </Box>
                      </Box>
                  </Paper>
                  </React.Fragment>
                ))}
              </Box>
            ) : (
              <Typography color="text.secondary" sx={{ mb: 3 }}>
                No THEN condition defined. Click the + button to add.
              </Typography>
            )}
          </Box>
        </Box>
      ) : ruleType === 'LAYER_GROUP' ? (
        // Layer Group Rules Form
        <Box sx={{ mt: 3 }}>
          <Typography variant="subtitle1" gutterBottom>Layer Grouping</Typography>

          {/* Display errors */}
          {errors.groupLayers && (
            <FormHelperText error sx={{ ml: 1, mb: 1 }}>
              {errors.groupLayers}
            </FormHelperText>
          )}

          <Box sx={{ backgroundColor: 'background.default', p: 2, borderRadius: 1, mt: 2 }}>
            <Typography variant="body2" color="text.secondary" paragraph>
              Group rules allow you to treat multiple layers as a single layer for randomization.
              This is useful for traits that need to be in different layers but should be treated
              as if they're in the same layer when rolling dice.
            </Typography>

            {/* Group Behavior */}
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
              Group Behavior:
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
              <Paper
                variant={formValues.groupBehavior === 'sync' ? "elevation" : "outlined"}
                elevation={formValues.groupBehavior === 'sync' ? 3 : 0}
                sx={{
                  p: 2,
                  flex: 1,
                  cursor: 'pointer',
                  borderColor: formValues.groupBehavior === 'sync' ? 'primary.main' : undefined,
                  bgcolor: formValues.groupBehavior === 'sync' ? 'primary.light' : undefined,
                  opacity: formValues.groupBehavior === 'sync' ? 1 : 0.7,
                }}
                onClick={() => handleFormChange('groupBehavior', 'sync')}
              >
                <Typography variant="subtitle2" color={formValues.groupBehavior === 'sync' ? 'primary' : 'text.primary'}>
                  Sync Values
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  All layers in the group will use the same trait whenever one is changed
                </Typography>
              </Paper>

              <Paper
                variant={formValues.groupBehavior === 'reference' ? "elevation" : "outlined"}
                elevation={formValues.groupBehavior === 'reference' ? 3 : 0}
                sx={{
                  p: 2,
                  flex: 1,
                  cursor: 'pointer',
                  borderColor: formValues.groupBehavior === 'reference' ? 'primary.main' : undefined,
                  bgcolor: formValues.groupBehavior === 'reference' ? 'primary.light' : undefined,
                  opacity: formValues.groupBehavior === 'reference' ? 1 : 0.7,
                }}
                onClick={() => handleFormChange('groupBehavior', 'reference')}
              >
                <Typography variant="subtitle2" color={formValues.groupBehavior === 'reference' ? 'primary' : 'text.primary'}>
                  Reference First
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  First layer with a selection will be used as reference for others
                </Typography>
              </Paper>
            </Box>

            <Typography variant="subtitle2" gutterBottom>
              Select layers to group together:
            </Typography>

            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 1 }}>
              {layers.map((layer) => (
                <Box key={layer.id} sx={{ flex: '1 1 300px', minWidth: '300px' }}>
                  <Paper
                    variant="outlined"
                    sx={{
                      p: 2,
                      bgcolor: groupLayers.includes(layer.id) ? 'primary.light' : 'background.paper',
                      borderColor: groupLayers.includes(layer.id) ? 'primary.main' : 'divider',
                      cursor: 'pointer',
                      '&:hover': {
                        borderColor: 'primary.main',
                        bgcolor: 'action.hover'
                      }
                    }}
                    onClick={() => handleToggleLayerInGroup(layer.id)}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Typography variant="body1">{layer.name}</Typography>
                      {groupLayers.includes(layer.id) && (
                        <Box
                          sx={{
                            width: 24,
                            height: 24,
                            borderRadius: '50%',
                            bgcolor: 'primary.main',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'primary.contrastText'
                          }}
                        >
                          ✓
                        </Box>
                      )}
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      {layer.traits?.length || 0} traits
                    </Typography>
                  </Paper>
                </Box>
              ))}
            </Box>
          </Box>
        </Box>
      ) : (
        // Trait Group Rules Form
        <Box sx={{ mt: 3 }}>
          <Typography variant="subtitle1" gutterBottom>Trait Grouping</Typography>

          {/* Display errors */}
          {errors.groupTraits && (
            <FormHelperText error sx={{ ml: 1, mb: 1 }}>
              {errors.groupTraits}
            </FormHelperText>
          )}

          <Box sx={{ backgroundColor: 'background.default', p: 2, borderRadius: 1, mt: 2 }}>
            <Typography variant="body2" color="text.secondary" paragraph>
              Trait grouping allows you to define groups of traits that should be treated as a single unit.
              This is useful for traits that need to be synchronized across different layers.
            </Typography>

            {/* Group Behavior */}
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
              Group Behavior:
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
              <Paper
                variant={formValues.groupBehavior === 'sync' ? "elevation" : "outlined"}
                elevation={formValues.groupBehavior === 'sync' ? 3 : 0}
                sx={{
                  p: 2,
                  flex: 1,
                  cursor: 'pointer',
                  borderColor: formValues.groupBehavior === 'sync' ? 'primary.main' : undefined,
                  bgcolor: formValues.groupBehavior === 'sync' ? 'primary.light' : undefined,
                  opacity: formValues.groupBehavior === 'sync' ? 1 : 0.7,
                }}
                onClick={() => handleFormChange('groupBehavior', 'sync')}
              >
                <Typography variant="subtitle2" color={formValues.groupBehavior === 'sync' ? 'primary' : 'text.primary'}>
                  Sync Values
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  All traits in the group will be selected together
                </Typography>
              </Paper>

              <Paper
                variant={formValues.groupBehavior === 'reference' ? "elevation" : "outlined"}
                elevation={formValues.groupBehavior === 'reference' ? 3 : 0}
                sx={{
                  p: 2,
                  flex: 1,
                  cursor: 'pointer',
                  borderColor: formValues.groupBehavior === 'reference' ? 'primary.main' : undefined,
                  bgcolor: formValues.groupBehavior === 'reference' ? 'primary.light' : undefined,
                  opacity: formValues.groupBehavior === 'reference' ? 1 : 0.7,
                }}
                onClick={() => handleFormChange('groupBehavior', 'reference')}
              >
                <Typography variant="subtitle2" color={formValues.groupBehavior === 'reference' ? 'primary' : 'text.primary'}>
                  Reference First
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  First trait with a selection will be used as reference for others
                </Typography>
              </Paper>
            </Box>

            <Typography variant="subtitle2" gutterBottom>
              Select traits to group together:
            </Typography>

            <Button
              variant="outlined"
              size="small"
              startIcon={<AddIcon />}
              onClick={() => {
                setGroupTraits(prev => [...prev, { layerId: '', traitIds: [] }])
              }}
              sx={{ mb: 2 }}
            >
              Add Trait Group
            </Button>

            {groupTraits.length > 0 ? (
              groupTraits.map((group, groupIndex) => (
                <Paper key={`trait-group-${groupIndex}`} elevation={1} sx={{ p: 2, mb: 2, borderRadius: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="subtitle2">Trait Group #{groupIndex + 1}</Typography>
                    <IconButton
                      color="error"
                      size="small"
                      onClick={() => {
                        setGroupTraits(prev => prev.filter((_, i) => i !== groupIndex))
                      }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Box>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <Box>
                      <FormControl size="small" sx={{ width: '200px' }}>
                        <InputLabel>Layer</InputLabel>
                        <Select
                          value={group.layerId || ''}
                          label="Layer"
                          onChange={(e) => {
                            const newGroupTraits = [...groupTraits]
                            newGroupTraits[groupIndex].layerId = e.target.value as string
                            newGroupTraits[groupIndex].traitIds = [] // Reset traits when layer changes
                            setGroupTraits(newGroupTraits)
                          }}
                        >
                          <MenuItem value=""><em>Select a Layer</em></MenuItem>
                          {layers.map((layer) => (
                            <MenuItem key={layer.id} value={layer.id}>
                              {layer.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Box>

                    <Box>
                      <FormControl size="small" disabled={!group.layerId} sx={{ width: '200px' }}>
                        <InputLabel>Traits</InputLabel>
                        <Select
                          multiple
                          value={group.traitIds || []}
                          label="Traits"
                          onChange={(e) => {
                            const newGroupTraits = [...groupTraits]
                            newGroupTraits[groupIndex].traitIds = e.target.value as string[]
                            setGroupTraits(newGroupTraits)
                          }}
                          renderValue={(selected) => {
                            const selectedTraits = getTraitsForLayer(group.layerId)
                              .filter(trait => (selected as string[]).includes(trait.id))
                              .map(trait => trait.name)
                            return selectedTraits.join(', ')
                          }}
                        >
                          {getTraitsForLayer(group.layerId).map((trait) => (
                            <MenuItem key={trait.id} value={trait.id}>
                              <Checkbox checked={group.traitIds.indexOf(trait.id) > -1} />
                              {trait.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Box>
                  </Box>
                </Paper>
              ))
            ) : (
              <Typography color="text.secondary">
                No trait groups defined. Click the + button to add a group.
              </Typography>
            )}
          </Box>
        </Box>
      )}

      {/* Form Actions */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 3, pt: 2, borderTop: 1, borderColor: 'divider' }}>
        <Button variant="outlined" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          startIcon={<SaveIcon />}
          disabled={
            !formValues.name.trim() ||
            (ruleType === 'LAYER_GROUP' && groupLayers.length < 2) ||
            (ruleType === 'TRAIT_GROUP' && groupTraits.length === 0)
          }
        >
          {rule ? 'Update Rule' : 'Save Rule'}
        </Button>
      </Box>
    </Box>
  )
}
