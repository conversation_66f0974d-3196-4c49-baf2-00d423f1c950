/**
 * V1 Compatible Rule Types for V2 Integration
 * Based on V1 Rules system from C:\__PRJ\__NFT-r5\zziipp\nft2-3\src\types\rules.ts
 */

/**
 * Rule types
 */
export type RuleType = 'IF_THEN' | 'LAYER_GROUP' | 'TRAIT_GROUP'

/**
 * Rule priority levels
 */
export type RulePriority = 'lowest' | 'low' | 'medium' | 'high' | 'highest'

/**
 * Priority configuration for UI display
 */
export const priorityConfig = {
  lowest: { label: 'Lowest', color: '#9e9e9e' },
  low: { label: 'Low', color: '#2196f3' },
  medium: { label: 'Medium', color: '#ff9800' },
  high: { label: 'High', color: '#f44336' },
  highest: { label: 'Highest', color: '#9c27b0' }
}

/**
 * Logical operators for rule conditions
 */
export type LogicalOperator = 'AND' | 'OR' | 'NOT'

/**
 * Rule condition
 */
export interface RuleCondition {
  id?: string
  type?: 'IF' | 'THEN'
  field?: string
  operator?: string
  value?: any
  logicalOperator?: LogicalOperator
  // For IF conditions
  layerId?: string | null
  traitId?: string | null
  // For THEN conditions
  targetLayerId?: string | null
  targetTraitId?: string | null
}

/**
 * Rule definition - V1 Compatible
 */
export interface Rule {
  id: string
  name: string
  description?: string
  type: RuleType
  conditions: RuleCondition[]
  order: number
  groupLayers?: string[]
  groupTraits?: {layerId: string, traitIds: string[]}[]
  groupBehavior?: 'sync' | 'reference'
  enabled: boolean
}

/**
 * Rule conflict detection result
 */
export interface RuleConflict {
  severity: 'error' | 'warning'
  message: string
  ruleIds: string[]
  affectedLayers?: string[]
}

/**
 * Rule validation result
 */
export interface RuleValidationResult {
  isValid: boolean
  warnings: string[]
  errors: string[]
}



/**
 * Rule form values for editing
 */
export interface RuleFormValues {
  id: string
  name: string
  description: string
  type: RuleType
  conditions: RuleCondition[]
  order: number
  groupLayers?: string[]
  groupTraits?: {layerId: string, traitIds: string[]}[]
  groupBehavior?: 'sync' | 'reference'
  enabled?: boolean
}

/**
 * Initial form values for different rule types
 */
export const initialFormValues: RuleFormValues = {
  id: '',
  name: '',
  description: '',
  type: 'IF_THEN',
  conditions: [],
  order: 1,
  groupLayers: [],
  enabled: true,
}

export const initialLayerGroupFormValues: RuleFormValues = {
  id: '',
  name: '',
  description: '',
  type: 'LAYER_GROUP',
  conditions: [],
  order: 1,
  groupLayers: [],
  groupBehavior: 'sync',
  enabled: true,
}

export const initialTraitGroupFormValues: RuleFormValues = {
  id: '',
  name: '',
  description: '',
  type: 'TRAIT_GROUP',
  conditions: [],
  order: 1,
  groupTraits: [],
  groupBehavior: 'sync',
  enabled: true,
}
