/**
 * Unified Memory Service
 * 
 * Tek bir merkezi hafıza sistemi - tüm persistence işlemlerini yönetir
 * - Zustand store state management
 * - IndexedDB persistent storage
 * - Image cache ve blob URL persistence
 * - Rules modal state persistence
 * - Real-time synchronization
 */

import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';
import { unifiedStorageService } from '../storage/unified-storage.service';

// Temporary types for compilation - will be replaced with actual imports
interface Rule {
  id: string;
  name: string;
  enabled: boolean;
  type?: string;
  conditions?: any[];
  priority?: string;
}

interface Layer {
  id: string;
  name: string;
  order?: number;
  traits?: Trait[];
}

interface Trait {
  id: string;
  name: string;
  imageUrl?: string;
  rarity?: number;
}

interface NFTSettings { }

interface Project {
  id: string;
  name: string;
  layers?: Layer[];
}

// ===== TYPES =====

interface ImageCacheEntry {
  id: string;
  layerName: string;
  traitName: string;
  dataUrl: string; // Base64 data URL for persistence
  blobUrl?: string; // Runtime blob URL
  timestamp: number;
  size: number;
}

interface RulesModalState {
  isOpen: boolean;
  activeTab: 'trait' | 'layer-group' | 'trait-group';
  editingRuleId: string | null;
  showForm: boolean;
  formData: any; // Current form state
  conflicts: any[];
  showConflicts: boolean;
}

interface UnifiedMemoryState {
  // Core Data
  currentProject: Project | null;
  projects: Project[];
  rules: Rule[];
  
  // UI State
  selectedLayerId?: string;
  selectedTraitIds: string[];
  
  // Rules Modal State (persistent)
  rulesModal: RulesModalState;
  
  // Image Cache (persistent)
  imageCache: Map<string, ImageCacheEntry>;
  
  // Loading States
  isLoading: boolean;
  loadingMessage?: string;
  
  // Actions
  setCurrentProject: (project: Project | null) => void;
  updateProject: (updates: Partial<Project>) => void;
  addRule: (rule: Rule) => void;
  updateRule: (ruleId: string, updates: Partial<Rule>) => void;
  deleteRule: (ruleId: string) => void;
  
  // Rules Modal Actions
  setRulesModalOpen: (open: boolean) => void;
  setRulesModalTab: (tab: 'trait' | 'layer-group' | 'trait-group') => void;
  setRulesModalEditingRule: (ruleId: string | null) => void;
  setRulesModalFormData: (data: any) => void;
  resetRulesModal: () => void;
  
  // Image Cache Actions
  cacheImage: (key: string, entry: Omit<ImageCacheEntry, 'timestamp'>) => void;
  getCachedImage: (key: string) => ImageCacheEntry | null;
  clearImageCache: () => void;
  
  // Persistence Actions
  saveToStorage: () => Promise<void>;
  loadFromStorage: () => Promise<void>;
  
  // Utility Actions
  setLoading: (loading: boolean, message?: string) => void;
  reset: () => void;
}

// ===== DEFAULT STATES =====

const defaultRulesModalState: RulesModalState = {
  isOpen: false,
  activeTab: 'trait',
  editingRuleId: null,
  showForm: false,
  formData: null,
  conflicts: [],
  showConflicts: false
};

// ===== STORE IMPLEMENTATION =====

export const useUnifiedMemory = create<UnifiedMemoryState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial State
        currentProject: null,
        projects: [],
        rules: [],
        selectedLayerId: undefined,
        selectedTraitIds: [],
        rulesModal: defaultRulesModalState,
        imageCache: new Map(),
        isLoading: false,
        loadingMessage: undefined,

        // Core Data Actions
        setCurrentProject: (project) => {
          set({ currentProject: project });
          get().saveToStorage();
        },

        updateProject: (updates) => {
          const { currentProject } = get();
          if (currentProject) {
            const updatedProject = { ...currentProject, ...updates };
            set({ 
              currentProject: updatedProject,
              projects: get().projects.map(p => 
                p.id === updatedProject.id ? updatedProject : p
              )
            });
            get().saveToStorage();
          }
        },

        addRule: (rule) => {
          set({ rules: [...get().rules, rule] });
          get().saveToStorage();
        },

        updateRule: (ruleId, updates) => {
          set({
            rules: get().rules.map(rule =>
              rule.id === ruleId ? { ...rule, ...updates } : rule
            )
          });
          get().saveToStorage();
        },

        deleteRule: (ruleId) => {
          set({ rules: get().rules.filter(rule => rule.id !== ruleId) });
          get().saveToStorage();
        },

        // Rules Modal Actions
        setRulesModalOpen: (open) => {
          set({
            rulesModal: { ...get().rulesModal, isOpen: open }
          });
        },

        setRulesModalTab: (tab) => {
          set({
            rulesModal: { ...get().rulesModal, activeTab: tab }
          });
        },

        setRulesModalEditingRule: (ruleId) => {
          set({
            rulesModal: { 
              ...get().rulesModal, 
              editingRuleId: ruleId,
              showForm: ruleId !== null 
            }
          });
        },

        setRulesModalFormData: (data) => {
          set({
            rulesModal: { ...get().rulesModal, formData: data }
          });
        },

        resetRulesModal: () => {
          set({ rulesModal: defaultRulesModalState });
        },

        // Image Cache Actions
        cacheImage: (key, entry) => {
          const cache = new Map(get().imageCache);
          cache.set(key, {
            ...entry,
            timestamp: Date.now()
          });
          set({ imageCache: cache });
          
          // Persist to storage asynchronously
          get().saveToStorage();
        },

        getCachedImage: (key) => {
          return get().imageCache.get(key) || null;
        },

        clearImageCache: () => {
          // Revoke all blob URLs before clearing
          const cache = get().imageCache;
          cache.forEach(entry => {
            if (entry.blobUrl) {
              try {
                URL.revokeObjectURL(entry.blobUrl);
              } catch (e) {
                // Ignore errors
              }
            }
          });
          
          set({ imageCache: new Map() });
          get().saveToStorage();
        },

        // Persistence Actions
        saveToStorage: async () => {
          try {
            const state = get();
            const { currentProject, rules, rulesModal, imageCache } = state;

            console.log('💾 Saving to storage...');

            // Save to IndexedDB
            if (currentProject) {
              await unifiedStorageService.saveProject(currentProject);
              await unifiedStorageService.saveRules(rules, currentProject.id);
            }

            // Save rules modal state
            await unifiedStorageService.saveItem(
              'ui_state',
              { rulesModal },
              'rules_modal_state'
            );

            // Save image cache (convert Map to Array for serialization)
            const imageCacheArray = Array.from(imageCache.entries());
            await unifiedStorageService.saveItem(
              'image_cache',
              imageCacheArray,
              'persistent_cache'
            );

            console.log('✅ Data saved to storage successfully');
          } catch (error) {
            console.error('Failed to save to storage:', error);
          }
        },

        loadFromStorage: async () => {
          try {
            set({ isLoading: true, loadingMessage: 'Loading data...' });

            console.log('📂 Loading from storage...');

            // Load current project
            const currentProject = await unifiedStorageService.loadCurrentProject();
            if (currentProject) {
              set({ currentProject });

              // Load rules for current project
              const rules = await unifiedStorageService.loadRules(currentProject.id);
              set({ rules });
            }

            // Load rules modal state
            try {
              const uiState = await unifiedStorageService.getItem('ui_state', 'rules_modal_state');
              if (uiState && uiState.rulesModal) {
                set({ rulesModal: { ...defaultRulesModalState, ...uiState.rulesModal } });
              }
            } catch (e) {
              console.warn('Could not load rules modal state:', e);
            }

            // Load image cache
            try {
              const imageCacheArray = await unifiedStorageService.getItem('image_cache', 'persistent_cache');
              if (Array.isArray(imageCacheArray)) {
                const imageCache = new Map(imageCacheArray);

                // SECURITY FIX: Don't regenerate blob URLs to avoid security errors
                // Only use data URLs for cached images
                const typedImageCache = new Map<string, ImageCacheEntry>();
                imageCache.forEach((entry: any, key: any) => {
                  const keyStr = String(key);
                  if (entry && typeof entry === 'object' && entry.dataUrl) {
                    const typedEntry = entry as ImageCacheEntry;
                    // Only keep data URL, remove any blob URL references
                    typedImageCache.set(keyStr, {
                      ...typedEntry,
                      blobUrl: undefined // Remove blob URL to prevent security issues
                    });
                  }
                });

                set({ imageCache: typedImageCache });
              }
            } catch (e) {
              console.warn('Could not load image cache:', e);
            }

            console.log('✅ Data loaded from storage successfully');
          } catch (error) {
            console.error('Failed to load from storage:', error);
          } finally {
            set({ isLoading: false, loadingMessage: undefined });
          }
        },

        // Utility Actions
        setLoading: (loading, message) => {
          set({ isLoading: loading, loadingMessage: message });
        },

        reset: () => {
          get().clearImageCache();
          set({
            currentProject: null,
            projects: [],
            rules: [],
            selectedLayerId: undefined,
            selectedTraitIds: [],
            rulesModal: defaultRulesModalState,
            imageCache: new Map(),
            isLoading: false,
            loadingMessage: undefined
          });
        }
      }),
      {
        name: 'unified-memory-store',
        // Only persist essential data, not runtime state
        partialize: (state) => ({
          currentProject: state.currentProject,
          projects: state.projects,
          rules: state.rules,
          selectedLayerId: state.selectedLayerId,
          selectedTraitIds: state.selectedTraitIds,
          rulesModal: state.rulesModal
        })
      }
    ),
    { name: 'Unified Memory Store' }
  )
);

// ===== UTILITY FUNCTIONS =====

/**
 * Initialize the unified memory system
 */
export const initializeUnifiedMemory = async () => {
  console.log('🚀 Initializing unified memory system...');
  const store = useUnifiedMemory.getState();
  await store.loadFromStorage();
  console.log('✅ Unified memory system initialized');
};

/**
 * Create image cache key
 */
export const createImageCacheKey = (
  layerName: string,
  traitName: string,
  fileSize?: number
): string => {
  return `${layerName}_${traitName}_${fileSize || 'unknown'}`;
};

/**
 * Convert File to persistent image cache entry
 * SECURITY FIX: Only create data URLs, no blob URLs
 */
export const fileToImageCacheEntry = async (
  file: File,
  layerName: string,
  traitName: string
): Promise<Omit<ImageCacheEntry, 'timestamp'>> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = () => {
      const dataUrl = reader.result as string;
      // SECURITY FIX: Don't create blob URL to avoid security errors

      resolve({
        id: `${layerName}_${traitName}_${file.size}`,
        layerName,
        traitName,
        dataUrl,
        blobUrl: undefined, // Don't create blob URLs
        size: file.size
      });
    };

    reader.onerror = () => reject(reader.error);
    reader.readAsDataURL(file);
  });
};

export default useUnifiedMemory;
