/**
 * Image Persistence Service
 * 
 * Trait thumbnail'larının refresh sonrası kaybolma sorununu ç<PERSON>zer
 * - File'ları IndexedDB'de data URL olarak saklar
 * - Refresh sonrası blob URL'leri yeniden oluşturur
 * - Memory-efficient caching strategy
 * - Automatic cleanup ve garbage collection
 */

import { useUnifiedMemory, createImageCacheKey, fileToImageCacheEntry } from './unified-memory.service';

// ===== TYPES =====

interface TraitImageData {
  layerName: string;
  traitName: string;
  fileName: string;
  dataUrl: string;
  fileSize: number;
  lastAccessed: number;
}

interface ImagePersistenceConfig {
  maxCacheSize: number; // bytes
  maxAge: number; // milliseconds
  compressionQuality: number;
}

// ===== CONFIGURATION =====

const DEFAULT_CONFIG: ImagePersistenceConfig = {
  maxCacheSize: 50 * 1024 * 1024, // 50MB
  maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  compressionQuality: 0.8
};

// ===== SERVICE IMPLEMENTATION =====

class ImagePersistenceService {
  private config: ImagePersistenceConfig;
  private compressionCanvas: HTMLCanvasElement | null = null;

  constructor(config: ImagePersistenceConfig = DEFAULT_CONFIG) {
    this.config = config;
  }

  /**
   * Store trait image for persistence
   */
  async storeTraitImage(
    file: File,
    layerName: string,
    traitName: string
  ): Promise<string> {
    try {
      // Create cache key
      const cacheKey = createImageCacheKey(layerName, traitName, file.size);
      
      // Check if already cached
      const { getCachedImage, cacheImage } = useUnifiedMemory.getState();
      const existing = getCachedImage(cacheKey);
      
      if (existing && existing.blobUrl) {
        // Update last accessed time
        existing.timestamp = Date.now();
        cacheImage(cacheKey, existing);
        return existing.blobUrl;
      }

      // Convert file to cache entry
      const cacheEntry = await fileToImageCacheEntry(file, layerName, traitName);
      
      // Compress if needed
      const compressedDataUrl = await this.compressImageIfNeeded(
        cacheEntry.dataUrl,
        file.size
      );
      
      // Update cache entry with compressed data
      const finalEntry = {
        ...cacheEntry,
        dataUrl: compressedDataUrl
      };
      
      // Store in unified memory
      cacheImage(cacheKey, finalEntry);
      
      // Return blob URL for immediate use
      return finalEntry.blobUrl || cacheEntry.dataUrl;
      
    } catch (error) {
      console.error('Failed to store trait image:', error);
      // Fallback to direct blob URL
      return URL.createObjectURL(file);
    }
  }

  /**
   * Retrieve trait image (with automatic blob URL regeneration)
   */
  async getTraitImage(
    layerName: string,
    traitName: string,
    fileSize?: number
  ): Promise<string | null> {
    try {
      const cacheKey = createImageCacheKey(layerName, traitName, fileSize);
      const { getCachedImage, cacheImage } = useUnifiedMemory.getState();
      const cached = getCachedImage(cacheKey);
      
      if (!cached) {
        return null;
      }
      
      // Update last accessed time
      cached.timestamp = Date.now();
      
      // SECURITY FIX: Don't use blob URLs, only return data URLs
      if (cached.dataUrl) {
        // Update last accessed time and return data URL directly
        cached.timestamp = Date.now();
        cacheImage(cacheKey, cached);
        return cached.dataUrl;
      }

      // If we only have blob URL, try to convert to data URL
      if (cached.blobUrl) {
        try {
          // Convert blob URL to data URL for security
          const response = await fetch(cached.blobUrl);
          const blob = await response.blob();
          const dataUrl = await this.blobToDataUrl(blob);

          // Update cache with data URL and remove blob URL
          const updatedEntry = {
            ...cached,
            dataUrl,
            blobUrl: undefined, // Remove blob URL to prevent future security issues
            timestamp: Date.now()
          };

          cacheImage(cacheKey, updatedEntry);
          return dataUrl;
        } catch (e) {
          console.warn('Failed to convert blob URL to data URL:', e);
          // Remove invalid entry
          const { removeCachedImage } = useUnifiedMemory.getState();
          removeCachedImage(cacheKey);
        }
      }
      
      return null;
      
    } catch (error) {
      console.error('Failed to get trait image:', error);
      return null;
    }
  }

  /**
   * Clean up old cached images
   */
  async cleanupOldImages(): Promise<void> {
    try {
      const { imageCache } = useUnifiedMemory.getState();
      const now = Date.now();
      let totalSize = 0;
      const entries = Array.from(imageCache.entries());
      
      // Calculate total size and filter old entries
      const validEntries = entries.filter(([key, entry]) => {
        const age = now - entry.timestamp;
        const isValid = age < this.config.maxAge;
        
        if (isValid) {
          totalSize += entry.size;
        } else {
          // Revoke old blob URLs
          if (entry.blobUrl) {
            try {
              URL.revokeObjectURL(entry.blobUrl);
            } catch (e) {
              // Ignore errors
            }
          }
        }
        
        return isValid;
      });
      
      // If total size exceeds limit, remove oldest entries
      if (totalSize > this.config.maxCacheSize) {
        validEntries.sort((a, b) => a[1].timestamp - b[1].timestamp);
        
        while (totalSize > this.config.maxCacheSize && validEntries.length > 0) {
          const [key, entry] = validEntries.shift()!;
          totalSize -= entry.size;
          
          // Revoke blob URL
          if (entry.blobUrl) {
            try {
              URL.revokeObjectURL(entry.blobUrl);
            } catch (e) {
              // Ignore errors
            }
          }
        }
      }
      
      // Update cache with cleaned entries
      const newCache = new Map(validEntries);
      useUnifiedMemory.setState({ imageCache: newCache });
      
      console.log(`Image cache cleanup: ${entries.length - validEntries.length} entries removed`);
      
    } catch (error) {
      console.error('Failed to cleanup old images:', error);
    }
  }

  /**
   * Compress image if it's too large
   */
  private async compressImageIfNeeded(
    dataUrl: string,
    originalSize: number
  ): Promise<string> {
    // Only compress if larger than 1MB
    if (originalSize < 1024 * 1024) {
      return dataUrl;
    }
    
    try {
      if (!this.compressionCanvas) {
        this.compressionCanvas = document.createElement('canvas');
      }
      
      const canvas = this.compressionCanvas;
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        return dataUrl;
      }
      
      // Load image
      const img = new Image();
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = dataUrl;
      });
      
      // Calculate new dimensions (max 800px)
      const maxDimension = 800;
      let { width, height } = img;
      
      if (width > maxDimension || height > maxDimension) {
        const ratio = Math.min(maxDimension / width, maxDimension / height);
        width *= ratio;
        height *= ratio;
      }
      
      // Draw compressed image
      canvas.width = width;
      canvas.height = height;
      ctx.drawImage(img, 0, 0, width, height);
      
      // Return compressed data URL
      return canvas.toDataURL('image/jpeg', this.config.compressionQuality);
      
    } catch (error) {
      console.error('Failed to compress image:', error);
      return dataUrl;
    }
  }

  /**
   * Convert data URL to blob
   */
  private async dataUrlToBlob(dataUrl: string): Promise<Blob> {
    const response = await fetch(dataUrl);
    return response.blob();
  }
}

// ===== SINGLETON INSTANCE =====

export const imagePersistenceService = new ImagePersistenceService();

// ===== UTILITY FUNCTIONS =====

/**
 * Initialize image persistence system
 */
export const initializeImagePersistence = async () => {
  console.log('🖼️ Initializing image persistence system...');
  // Clean up old images on startup
  await imagePersistenceService.cleanupOldImages();
  
  // Set up periodic cleanup (every hour)
  setInterval(() => {
    imagePersistenceService.cleanupOldImages();
  }, 60 * 60 * 1000);
  
  console.log('✅ Image persistence system initialized');
};

/**
 * Store trait image with automatic persistence
 */
export const storeTraitImage = (
  file: File,
  layerName: string,
  traitName: string
): Promise<string> => {
  return imagePersistenceService.storeTraitImage(file, layerName, traitName);
};

/**
 * Get trait image with automatic blob URL regeneration
 */
export const getTraitImage = (
  layerName: string,
  traitName: string,
  fileSize?: number
): Promise<string | null> => {
  return imagePersistenceService.getTraitImage(layerName, traitName, fileSize);
};

export default imagePersistenceService;
