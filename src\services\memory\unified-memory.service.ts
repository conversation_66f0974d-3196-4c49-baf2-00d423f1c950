/**
 * Unified Memory Service
 * 
 * Tek bir merkezi hafıza sistemi - tüm persistence işlemlerini yönetir
 * - Zustand store state management
 * - IndexedDB persistent storage
 * - Image cache ve blob URL persistence
 * - Rules modal state persistence
 * - Real-time synchronization
 */

import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';
// import { Rule, Layer, Trait, NFTSettings, Project } from '@/types';
// import { unifiedStorageService } from '@/services/storage/unified-storage.service';

// Temporary types for compilation
interface Rule { id: string; name: string; enabled: boolean; }
interface Layer { id: string; name: string; }
interface Trait { id: string; name: string; }
interface NFTSettings { }
interface Project { id: string; name: string; }

// ===== TYPES =====

interface ImageCacheEntry {
  id: string;
  layerName: string;
  traitName: string;
  dataUrl: string; // Base64 data URL for persistence
  blobUrl?: string; // Runtime blob URL
  timestamp: number;
  size: number;
}

interface RulesModalState {
  isOpen: boolean;
  activeTab: 'trait' | 'layer-group' | 'trait-group';
  editingRuleId: string | null;
  showForm: boolean;
  formData: any; // Current form state
  conflicts: any[];
  showConflicts: boolean;
}

interface UnifiedMemoryState {
  // Core Data
  currentProject: Project | null;
  projects: Project[];
  rules: Rule[];
  
  // UI State
  selectedLayerId?: string;
  selectedTraitIds: string[];
  
  // Rules Modal State (persistent)
  rulesModal: RulesModalState;
  
  // Image Cache (persistent)
  imageCache: Map<string, ImageCacheEntry>;
  
  // Loading States
  isLoading: boolean;
  loadingMessage?: string;
  
  // Actions
  setCurrentProject: (project: Project | null) => void;
  updateProject: (updates: Partial<Project>) => void;
  addRule: (rule: Rule) => void;
  updateRule: (ruleId: string, updates: Partial<Rule>) => void;
  deleteRule: (ruleId: string) => void;
  
  // Rules Modal Actions
  setRulesModalOpen: (open: boolean) => void;
  setRulesModalTab: (tab: 'trait' | 'layer-group' | 'trait-group') => void;
  setRulesModalEditingRule: (ruleId: string | null) => void;
  setRulesModalFormData: (data: any) => void;
  resetRulesModal: () => void;
  
  // Image Cache Actions
  cacheImage: (key: string, entry: Omit<ImageCacheEntry, 'timestamp'>) => void;
  getCachedImage: (key: string) => ImageCacheEntry | null;
  clearImageCache: () => void;
  
  // Persistence Actions
  saveToStorage: () => Promise<void>;
  loadFromStorage: () => Promise<void>;
  
  // Utility Actions
  setLoading: (loading: boolean, message?: string) => void;
  reset: () => void;
}

// ===== DEFAULT STATES =====

const defaultRulesModalState: RulesModalState = {
  isOpen: false,
  activeTab: 'trait',
  editingRuleId: null,
  showForm: false,
  formData: null,
  conflicts: [],
  showConflicts: false
};

// ===== STORE IMPLEMENTATION =====

export const useUnifiedMemory = create<UnifiedMemoryState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial State
        currentProject: null,
        projects: [],
        rules: [],
        selectedLayerId: undefined,
        selectedTraitIds: [],
        rulesModal: defaultRulesModalState,
        imageCache: new Map(),
        isLoading: false,
        loadingMessage: undefined,

        // Core Data Actions
        setCurrentProject: (project) => {
          set({ currentProject: project });
          get().saveToStorage();
        },

        updateProject: (updates) => {
          const { currentProject } = get();
          if (currentProject) {
            const updatedProject = { ...currentProject, ...updates };
            set({ 
              currentProject: updatedProject,
              projects: get().projects.map(p => 
                p.id === updatedProject.id ? updatedProject : p
              )
            });
            get().saveToStorage();
          }
        },

        addRule: (rule) => {
          set({ rules: [...get().rules, rule] });
          get().saveToStorage();
        },

        updateRule: (ruleId, updates) => {
          set({
            rules: get().rules.map(rule =>
              rule.id === ruleId ? { ...rule, ...updates } : rule
            )
          });
          get().saveToStorage();
        },

        deleteRule: (ruleId) => {
          set({ rules: get().rules.filter(rule => rule.id !== ruleId) });
          get().saveToStorage();
        },

        // Rules Modal Actions
        setRulesModalOpen: (open) => {
          set({
            rulesModal: { ...get().rulesModal, isOpen: open }
          });
        },

        setRulesModalTab: (tab) => {
          set({
            rulesModal: { ...get().rulesModal, activeTab: tab }
          });
        },

        setRulesModalEditingRule: (ruleId) => {
          set({
            rulesModal: { 
              ...get().rulesModal, 
              editingRuleId: ruleId,
              showForm: ruleId !== null 
            }
          });
        },

        setRulesModalFormData: (data) => {
          set({
            rulesModal: { ...get().rulesModal, formData: data }
          });
        },

        resetRulesModal: () => {
          set({ rulesModal: defaultRulesModalState });
        },

        // Image Cache Actions
        cacheImage: (key, entry) => {
          const cache = new Map(get().imageCache);
          cache.set(key, {
            ...entry,
            timestamp: Date.now()
          });
          set({ imageCache: cache });
          
          // Persist to IndexedDB asynchronously
          get().saveToStorage();
        },

        getCachedImage: (key) => {
          return get().imageCache.get(key) || null;
        },

        clearImageCache: () => {
          // Revoke all blob URLs before clearing
          const cache = get().imageCache;
          cache.forEach(entry => {
            if (entry.blobUrl) {
              try {
                URL.revokeObjectURL(entry.blobUrl);
              } catch (e) {
                // Ignore errors
              }
            }
          });
          
          set({ imageCache: new Map() });
          get().saveToStorage();
        },

        // Persistence Actions
        saveToStorage: async () => {
          try {
            const state = get();
            const { currentProject, projects, rules, rulesModal, imageCache } = state;
            
            // Save to IndexedDB
            if (currentProject) {
              await unifiedStorageService.saveProject(currentProject);
              await unifiedStorageService.saveRules(rules, currentProject.id);
            }
            
            // Save rules modal state
            await unifiedStorageService.saveItem(
              'ui_state',
              { rulesModal },
              'rules_modal_state'
            );
            
            // Save image cache (convert Map to Array for serialization)
            const imageCacheArray = Array.from(imageCache.entries());
            await unifiedStorageService.saveItem(
              'image_cache',
              imageCacheArray,
              'persistent_cache'
            );
            
          } catch (error) {
            console.error('Failed to save to storage:', error);
          }
        },

        loadFromStorage: async () => {
          try {
            set({ isLoading: true, loadingMessage: 'Loading data...' });
            
            // Load current project
            const currentProject = await unifiedStorageService.loadCurrentProject();
            if (currentProject) {
              set({ currentProject });
              
              // Load rules for current project
              const rules = await unifiedStorageService.loadRules(currentProject.id);
              set({ rules });
            }
            
            // Load rules modal state
            try {
              const uiState = await unifiedStorageService.getItem('ui_state', 'rules_modal_state');
              if (uiState && uiState.rulesModal) {
                set({ rulesModal: { ...defaultRulesModalState, ...uiState.rulesModal } });
              }
            } catch (e) {
              console.warn('Could not load rules modal state:', e);
            }
            
            // Load image cache
            try {
              const imageCacheArray = await unifiedStorageService.getItem('image_cache', 'persistent_cache');
              if (Array.isArray(imageCacheArray)) {
                const imageCache = new Map(imageCacheArray);
                
                // SECURITY FIX: Don't regenerate blob URLs to avoid security errors
                // Keep only data URLs for cached images
                imageCache.forEach((entry, key) => {
                  if (entry.blobUrl && !entry.dataUrl) {
                    // If we only have blob URL, try to convert to data URL
                    try {
                      fetch(entry.blobUrl)
                        .then(res => res.blob())
                        .then(blob => {
                          const reader = new FileReader();
                          reader.onload = () => {
                            if (typeof reader.result === 'string') {
                              entry.dataUrl = reader.result;
                              entry.blobUrl = undefined; // Remove blob URL
                              imageCache.set(key, entry);
                            }
                          };
                          reader.readAsDataURL(blob);
                        })
                        .catch(e => {
                          console.warn('Failed to convert blob URL to data URL:', e);
                          // Remove invalid blob URL entry
                          imageCache.delete(key);
                        });
                    } catch (e) {
                      console.warn('Failed to process cached image:', e);
                      imageCache.delete(key);
                    }
                  }
                });
                
                set({ imageCache });
              }
            } catch (e) {
              console.warn('Could not load image cache:', e);
            }
            
          } catch (error) {
            console.error('Failed to load from storage:', error);
          } finally {
            set({ isLoading: false, loadingMessage: undefined });
          }
        },

        // Utility Actions
        setLoading: (loading, message) => {
          set({ isLoading: loading, loadingMessage: message });
        },

        reset: () => {
          get().clearImageCache();
          set({
            currentProject: null,
            projects: [],
            rules: [],
            selectedLayerId: undefined,
            selectedTraitIds: [],
            rulesModal: defaultRulesModalState,
            imageCache: new Map(),
            isLoading: false,
            loadingMessage: undefined
          });
        }
      }),
      {
        name: 'unified-memory-store',
        // Only persist essential data, not runtime state
        partialize: (state) => ({
          currentProject: state.currentProject,
          projects: state.projects,
          rules: state.rules,
          selectedLayerId: state.selectedLayerId,
          selectedTraitIds: state.selectedTraitIds
        }),
        // Custom serialization for Map objects
        serialize: (state) => {
          return JSON.stringify({
            ...state,
            imageCache: Array.from(state.imageCache.entries())
          });
        },
        deserialize: (str) => {
          const parsed = JSON.parse(str);
          return {
            ...parsed,
            imageCache: new Map(parsed.imageCache || [])
          };
        }
      }
    ),
    { name: 'Unified Memory Store' }
  )
);

// ===== UTILITY FUNCTIONS =====

/**
 * Initialize the unified memory system
 */
export const initializeUnifiedMemory = async () => {
  const store = useUnifiedMemory.getState();
  await store.loadFromStorage();
};

/**
 * Create stable image cache key
 * Uses layer name and trait name for consistent caching across sessions
 */
export const createImageCacheKey = (
  layerName: string,
  traitName: string,
  fileSize?: number
): string => {
  // Normalize inputs for consistent cache keys
  const normalizedLayer = layerName.toLowerCase().replace(/[^a-z0-9]/g, '_');
  const normalizedTrait = traitName.toLowerCase().replace(/[^a-z0-9]/g, '_');

  // Use layer and trait names as primary key, fileSize as secondary identifier
  return `img_${normalizedLayer}_${normalizedTrait}_${fileSize || 'unknown'}`;
};

/**
 * Convert File to persistent image cache entry
 * SECURITY FIX: Only create data URLs, no blob URLs
 */
export const fileToImageCacheEntry = async (
  file: File,
  layerName: string,
  traitName: string
): Promise<Omit<ImageCacheEntry, 'timestamp'>> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = () => {
      const dataUrl = reader.result as string;
      // SECURITY FIX: Don't create blob URL to avoid security errors

      resolve({
        id: `${layerName}_${traitName}_${file.size}`,
        layerName,
        traitName,
        dataUrl,
        blobUrl: undefined, // Don't create blob URLs
        size: file.size
      });
    };

    reader.onerror = () => reject(reader.error);
    reader.readAsDataURL(file);
  });
};

export default useUnifiedMemory;
