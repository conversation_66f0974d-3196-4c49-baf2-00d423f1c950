import { Layer } from '../../domain/entities/Layer'
import { TraitGroup } from '../../domain/entities/TraitGroup'
import { Trait } from '../../shared/types/Project.types'
import { storeTraitImage } from '../../services/memory/image-persistence.service'

export interface ImportProgress {
  stage: 'scanning' | 'processing' | 'creating' | 'complete'
  current: number
  total: number
  message: string
}

export interface ImportResult {
  success: boolean
  layers: Layer[]
  errors: string[]
  warnings: string[]
  originalFiles?: FileList // Add original files for content analysis
}

export interface ImportOptions {
  createSubfolders: boolean
  supportedFormats: string[]
  onProgress?: (progress: ImportProgress) => void
}

/**
 * Service for importing layers from file system
 */
export class LayerImportService {
  private readonly defaultOptions: ImportOptions = {
    createSubfolders: true,
    supportedFormats: ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg', '.tiff', '.tif'],
    onProgress: undefined
  }

  /**
   * Import layers from selected directory
   */
  async importFromDirectory(options: Partial<ImportOptions> = {}): Promise<ImportResult> {
    const opts = { ...this.defaultOptions, ...options }

    try {
      // Check if File System Access API is supported
      if (this.isFileSystemAccessSupported()) {
        return await this.importFromDirectoryHandle(opts)
      } else {
        return await this.importFromFileInput(opts)
      }
    } catch (error) {
      return {
        success: false,
        layers: [],
        errors: [error instanceof Error ? error.message : 'Unknown error occurred'],
        warnings: []
      }
    }
  }

  /**
   * Check if File System Access API is supported
   */
  private isFileSystemAccessSupported(): boolean {
    return 'showDirectoryPicker' in window
  }

  /**
   * Import using File System Access API (Chrome, Edge)
   */
  private async importFromDirectoryHandle(options: ImportOptions): Promise<ImportResult> {
    // Request directory access
    const directoryHandle = await this.selectDirectory()
    if (!directoryHandle) {
      return {
        success: false,
        layers: [],
        errors: ['No directory selected'],
        warnings: []
      }
    }

    // Scan directory structure
    options.onProgress?.({
      stage: 'scanning',
      current: 0,
      total: 0,
      message: 'Scanning directory structure...'
    })

    const fileStructure = await this.scanDirectory(directoryHandle, options.supportedFormats)

    console.log('Scanned directory structure:', fileStructure.filter(item => item.type === 'directory').length, 'directories')
    console.log('Total layers created:', fileStructure.filter(item => item.type === 'directory').length)

    if (fileStructure.length === 0) {
      return {
        success: false,
        layers: [],
        errors: ['No supported image files found in the selected directory'],
        warnings: []
      }
    }

    // Process files and create layers
    options.onProgress?.({
      stage: 'processing',
      current: 0,
      total: fileStructure.length,
      message: 'Processing image files...'
    })

    const layers = await this.createLayersFromStructure(fileStructure, options)



    options.onProgress?.({
      stage: 'complete',
      current: layers.length,
      total: layers.length,
      message: `Successfully imported ${layers.length} layers`
    })

    return {
      success: true,
      layers,
      errors: [],
      warnings: []
    }
  }

  /**
   * Import using traditional file input (Firefox, Safari)
   */
  private async importFromFileInput(options: ImportOptions): Promise<ImportResult> {
    const files = await this.selectFiles()
    if (!files || files.length === 0) {
      return {
        success: false,
        layers: [],
        errors: ['No files selected'],
        warnings: []
      }
    }

    options.onProgress?.({
      stage: 'processing',
      current: 0,
      total: files.length,
      message: 'Processing selected files...'
    })

    const layers = await this.createLayersFromFiles(files, options)

    options.onProgress?.({
      stage: 'complete',
      current: layers.length,
      total: layers.length,
      message: `Successfully imported ${layers.length} layers`
    })

    // Convert File[] to FileList for content analysis
    const fileList = this.createFileListFromArray(files)

    return {
      success: true,
      layers,
      errors: [],
      warnings: [],
      originalFiles: fileList
    }
  }

  /**
   * Select files using traditional file input (Firefox fallback)
   */
  private async selectFiles(): Promise<File[] | null> {
    return new Promise((resolve) => {
      const input = document.createElement('input')
      input.type = 'file'
      input.multiple = true
      input.accept = this.defaultOptions.supportedFormats.join(',')
      input.webkitdirectory = true // Allow directory selection in supported browsers

      input.onchange = (event) => {
        const target = event.target as HTMLInputElement
        if (target.files && target.files.length > 0) {
          resolve(Array.from(target.files))
        } else {
          resolve(null)
        }
      }

      input.oncancel = () => resolve(null)

      // Trigger file dialog
      input.click()
    })
  }

  /**
   * Select directory using File System Access API
   */
  private async selectDirectory(): Promise<FileSystemDirectoryHandle | null> {
    try {
      // Check if File System Access API is supported
      if (!('showDirectoryPicker' in window)) {
        throw new Error('File System Access API is not supported in this browser')
      }

      const directoryHandle = await (window as any).showDirectoryPicker({
        mode: 'read'
      })

      return directoryHandle
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        // User cancelled the dialog
        return null
      }
      throw error
    }
  }

  /**
   * Scan directory and build file structure
   */
  private async scanDirectory(
    directoryHandle: FileSystemDirectoryHandle,
    supportedFormats: string[]
  ): Promise<DirectoryStructure[]> {
    const structure: DirectoryStructure[] = []

    for await (const [name, handle] of (directoryHandle as any).entries()) {
      if (handle.kind === 'directory') {
        // Recursively scan subdirectories
        const subStructure = await this.scanDirectory(handle, supportedFormats)
        if (subStructure.length > 0) {
          structure.push({
            name,
            type: 'directory',
            handle,
            children: subStructure
          })
        }
      } else if (handle.kind === 'file') {
        // Check if file is a supported image format
        const extension = this.getFileExtension(name)
        if (supportedFormats.includes(extension)) {
          structure.push({
            name,
            type: 'file',
            handle,
            extension
          })
        }
      }
    }

    return structure
  }

  /**
   * Create layers from File objects (Firefox fallback)
   */
  private async createLayersFromFiles(files: File[], options: ImportOptions): Promise<Layer[]> {
    const layers: Layer[] = []
    const layerMap = new Map<string, { files: File[], subDirs: Map<string, File[]>, order: number }>()

    console.log('🔄 Processing files for Firefox:', files.length)

    // Group files by directory structure with improved hierarchy handling
    for (const file of files) {
      const pathParts = file.webkitRelativePath.split('/').filter(part => part.length > 0)

      // Debug log for path analysis (development only)
      if (process.env.NODE_ENV === 'development') {
        console.log(`🔍 Analyzing path: ${file.webkitRelativePath}`)
        console.log(`  📁 Path parts: [${pathParts.join(', ')}] (length: ${pathParts.length})`)
      }

      if (pathParts.length < 2) {
        if (process.env.NODE_ENV === 'development') {
          console.log(`  ⏭️ Skipping: file in root directory`)
        }
        continue // Skip files in root
      }

      const layerName = pathParts[1] // First directory level (skip root folder)
      const fileName = pathParts[pathParts.length - 1]

      // Skip non-image files
      if (!this.isImageFile(fileName)) {
        if (process.env.NODE_ENV === 'development') {
          console.log(`  ⏭️ Skipping: not an image file (${fileName})`)
        }
        continue
      }

      if (process.env.NODE_ENV === 'development') {
        console.log(`  🎯 Layer: ${layerName}, File: ${fileName}`)
      }

      if (!layerMap.has(layerName)) {
        layerMap.set(layerName, {
          files: [],
          subDirs: new Map<string, File[]>(),
          order: layerMap.size
        })
      }

      const layerData = layerMap.get(layerName)!

      // Improved nested directory structure handling - LIMIT TO FIRST LEVEL ONLY
      // Check if there are intermediate directories between layer and file
      const hasSubdirectories = pathParts.length > 3

      if (hasSubdirectories) {
        // ONLY use the FIRST subdirectory level for grouping (Chrome-compatible behavior)
        // Examples:
        // root/Eye Color/Double Color/Blue-Green.png -> subDirName = "Double Color" (NOT "Double Color/Blue-Green")
        // root/Eye Color/Single Color/Blue.png -> subDirName = "Single Color" (NOT "Single Color/Blue")
        const firstSubDir = pathParts[2] // Only take the first subdirectory level
        const subDirName = firstSubDir

        if (process.env.NODE_ENV === 'development') {
          console.log(`  📂 First-level subdirectory detected: ${subDirName}`)
          console.log(`  🔍 Full path parts: [${pathParts.join(' > ')}]`)
          console.log(`  ✂️ Using only first subdirectory: "${firstSubDir}"`)
        }

        if (!layerData.subDirs.has(subDirName)) {
          layerData.subDirs.set(subDirName, [])
        }
        layerData.subDirs.get(subDirName)!.push(file)
      } else {
        // File is directly in layer directory (pathParts.length === 3: root/layer/file.png)
        if (process.env.NODE_ENV === 'development') {
          console.log(`  📄 Direct file in layer`)
        }
        layerData.files.push(file)
      }
    }

    // Create layers from grouped files
    for (const [layerName, { files: layerFiles, subDirs, order }] of layerMap) {
      const traits: Trait[] = []
      const traitGroups: TraitGroup[] = []



      // Process direct files as traits
      for (const file of layerFiles) {
        const trait = await this.createTraitFromFileObject(file, layerName)
        if (trait) {
          traits.push(trait)
        }
      }

      // Process subdirectories as trait groups - ONLY if there are actually subdirectories
      if (subDirs.size > 0) {
        for (const [subDirName, subDirFiles] of subDirs) {
          const groupTraits: Trait[] = []

          for (const file of subDirFiles) {
            const trait = await this.createTraitFromFileObject(file, layerName)
            if (trait) {
              groupTraits.push(trait)
            }
          }

          if (groupTraits.length > 0) {
            const traitGroup = new TraitGroup({
              name: subDirName,
              layerId: '', // Will be set when added to layer
              traits: groupTraits,
              rarityConstraint: {
                min: 30,
                max: 70,
                isLocked: false
              }
            })
            traitGroups.push(traitGroup)
            traits.push(...groupTraits) // Add to layer's traits array too
          }
        }
      }

      if (traits.length > 0) {
        const layer = new Layer({
          name: layerName,
          path: `/${layerName}`,
          order,
          traits,
          isVisible: true, // Explicitly set visibility
          isLocked: false  // Explicitly set lock state
        })

        // Add trait groups to layer only if there are any
        if (traitGroups.length > 0) {
          traitGroups.forEach(group => {
            layer.addTraitGroup(group)
          })
        }

        layers.push(layer)
      }
    }

    return layers.sort((a, b) => (a.order || 0) - (b.order || 0))
  }

  /**
   * Create layers from directory structure
   */
  private async createLayersFromStructure(
    structure: DirectoryStructure[],
    options: ImportOptions
  ): Promise<Layer[]> {
    const layers: Layer[] = []
    let order = 0

    for (const item of structure) {
      if (item.type === 'directory' && options.createSubfolders) {
        // Create layer from directory
        const layer = await this.createLayerFromDirectory(item, order++)
        if (layer) {
          layers.push(layer)
        }
      } else if (item.type === 'file') {
        // Create layer from single file (if not in a directory structure)
        const layer = await this.createLayerFromFile(item, order++)
        if (layer) {
          layers.push(layer)
        }
      }
    }
    return layers
  }

  /**
   * Create layer from directory
   */
  private async createLayerFromDirectory(
    directory: DirectoryStructure,
    order: number
  ): Promise<Layer | null> {
    if (!directory.children) {
      return null
    }

    const traits: Trait[] = []
    const traitGroups: TraitGroup[] = []

    // Process directory contents - separate direct files from subdirectories
    const directFiles = directory.children?.filter(child => child.type === 'file') || []
    const subdirectories = directory.children?.filter(child => child.type === 'directory') || []

    // Process direct files as ungrouped traits
    const directTraits: Trait[] = []
    for (const file of directFiles) {
      if (file.handle) {
        const trait = await this.createTraitFromFile(file.handle as FileSystemFileHandle, file.name, directory.name)
        if (trait) {
          directTraits.push(trait)
        }
      }
    }

    // Distribute rarity evenly among direct traits
    if (directTraits.length > 0) {
      this.distributeRarityEvenly(directTraits)
      traits.push(...directTraits)
    }

    // Process subdirectories as trait groups
    for (const subdir of subdirectories) {
      const group = await this.createTraitGroupFromDirectory(subdir, directory.name)
      if (group) {
        traitGroups.push(group)
        // Also add group traits to layer's traits array
        traits.push(...group.traits)
      }
    }

    const totalTraits = traits.length

    if (totalTraits === 0) {
      return null
    }

    // Create layer
    const layer = new Layer({
      name: directory.name,
      path: `/${directory.name}`,
      order,
      traits,
      isVisible: true, // Explicitly set visibility
      isLocked: false  // Explicitly set lock state
    })

    // Add trait groups to layer
    traitGroups.forEach(group => {
      layer.addTraitGroup(group)
    })
    return layer
  }

  /**
   * Create trait group from subdirectory
   */
  private async createTraitGroupFromDirectory(
    directory: DirectoryStructure,
    layerName: string
  ): Promise<TraitGroup | null> {
    if (!directory.children) {
      return null
    }

    const traits: Trait[] = []

    // Recursively process all files in this subdirectory
    await this.processDirectoryRecursively(directory.children, traits, 0, `        `)

    if (traits.length === 0) {
      return null
    }

    // Traits already have proper rarity values assigned during processing

    // Create trait group with default rarity constraints
    const group = new TraitGroup({
      name: directory.name,
      layerId: '', // Will be set by layer
      traits,
      rarityConstraint: {
        min: 20, // Default 20% minimum
        max: 80, // Default 80% maximum
        isLocked: false // Not locked by default
      },
      isExpanded: false, // Collapsed by default
      order: 0
    })


    return group
  }

  /**
   * Recursively process directory contents to find all image files
   */
  private async processDirectoryRecursively(
    children: DirectoryStructure[],
    traits: Trait[],
    depth = 0,
    customIndent?: string
  ): Promise<void> {
    const indent = customIndent || '  '.repeat(depth + 3)

    // First pass: collect all traits with temporary rarity
    const tempTraits: Trait[] = []

    for (const item of children) {
      if (item.type === 'file' && item.handle) {
        const trait = await this.createTraitFromFile(item.handle as FileSystemFileHandle, item.name, 'TraitGroup')
        if (trait) {
          tempTraits.push(trait)
        }
      } else if (item.type === 'directory' && item.children) {
        await this.processDirectoryRecursively(item.children, tempTraits, depth + 1, customIndent)
      }
    }

    // Second pass: calculate and assign proper rarity values
    if (tempTraits.length > 0) {
      const equalRarity = parseFloat((100 / tempTraits.length).toFixed(2))

      tempTraits.forEach((trait, index) => {
        if (index === tempTraits.length - 1) {
          // Last trait gets remaining rarity to ensure total = 100%
          const usedRarity = equalRarity * (tempTraits.length - 1)
          trait.rarity = parseFloat((100 - usedRarity).toFixed(2))
        } else {
          trait.rarity = equalRarity
        }
        traits.push(trait)
      })


    }
  }

  /**
   * Create layer from single file
   */
  private async createLayerFromFile(
    file: DirectoryStructure,
    order: number
  ): Promise<Layer | null> {
    if (file.type !== 'file' || !file.handle) return null

    // Create layer with single trait
    const layerName = this.getFileNameWithoutExtension(file.name)
    const trait = await this.createTraitFromFile(file.handle as FileSystemFileHandle, file.name, layerName)
    if (!trait) return null
    const layer = new Layer({
      name: layerName,
      path: `/${file.name}`,
      order,
      traits: [trait],
      isVisible: true, // Explicitly set visibility
      isLocked: false  // Explicitly set lock state
    })

    return layer
  }

  /**
   * Create trait from File object (Firefox fallback)
   */
  private async createTraitFromFileObject(file: File, layerName: string): Promise<Trait | null> {
    try {
      const traitName = this.getFileNameWithoutExtension(file.name)
      const traitPath = file.webkitRelativePath || `/${file.name}`

      // Generate stable ID for trait
      const stableId = this.generateStableTraitId(layerName, traitPath)

      // Store image persistently and get stable URL
      let imageUrl: string
      try {
        imageUrl = await storeTraitImage(file, layerName, traitName)
      } catch (error) {
        console.error(`Failed to store image for ${file.name}:`, error)
        // Fallback to blob URL for immediate display
        imageUrl = URL.createObjectURL(file)
      }

      const trait: Trait = {
        id: stableId,
        name: traitName,
        layerId: '', // Will be set by layer
        imagePath: traitPath,
        fileName: file.name,
        filePath: traitPath,
        imageUrl,
        rarity: 1, // Minimum non-zero value, will be calculated by distributeRarityEvenly
        isLocked: false,
        isEnabled: true,
        metadata: {
          fileSize: file.size,
          dimensions: { width: 0, height: 0 }, // Will be updated when image loads
          detectedFeatures: [],
          colorPalette: [],
          style: undefined
        }
      }

      return trait
    } catch (error) {
      console.error(`Failed to process file ${file.name}:`, error)
      return null
    }
  }

  /**
   * Create trait from file
   */
  private async createTraitFromFile(
    fileHandle: FileSystemFileHandle,
    fileName: string,
    layerName: string = 'Unknown'
  ): Promise<Trait | null> {
    try {
      const file = await fileHandle.getFile()
      const traitName = this.getFileNameWithoutExtension(fileName)
      const traitPath = `/${fileName}`

      // Generate stable ID for trait
      const stableId = this.generateStableTraitId(layerName, traitPath)

      // Store image persistently and get stable URL
      let imageUrl: string
      try {
        imageUrl = await storeTraitImage(file, layerName, traitName)
      } catch (error) {
        console.error(`Failed to store image for ${fileName}:`, error)
        // Fallback to blob URL for immediate display
        imageUrl = URL.createObjectURL(file)
      }

      const trait: Trait = {
        id: stableId,
        name: traitName,
        layerId: '', // Will be set by layer
        imagePath: traitPath,
        fileName,
        filePath: traitPath,
        imageUrl,
        rarity: 1, // Minimum non-zero value, will be calculated by distributeRarityEvenly
        isLocked: false,
        isEnabled: true,
        metadata: {
          fileSize: file.size,
          dimensions: { width: 0, height: 0 }, // Will be updated when image loads
          detectedFeatures: [],
          colorPalette: [],
          style: undefined
        }
      }

      return trait
    } catch (error) {
      console.error(`Failed to process file ${fileName}:`, error)
      return null
    }
  }

  /**
   * Distribute rarity evenly among traits (V1 style)
   */
  private distributeRarityEvenly(traits: Trait[]): void {
    if (traits.length === 0) return

    // Calculate equal rarity for each trait
    const equalRarity = parseFloat((100 / traits.length).toFixed(2))

    // Assign equal rarity to all traits except the last one
    for (let i = 0; i < traits.length - 1; i++) {
      traits[i].rarity = equalRarity
    }

    // Calculate remaining rarity for the last trait to ensure total = 100%
    const usedRarity = equalRarity * (traits.length - 1)
    const remainingRarity = parseFloat((100 - usedRarity).toFixed(2))

    if (traits.length > 0) {
      traits[traits.length - 1].rarity = remainingRarity
    }


  }

  /**
   * Generate stable trait ID based on layer name and trait path
   * This ensures same traits get same IDs across imports
   */
  private generateStableTraitId(layerName: string, traitPath: string): string {
    // Create a stable hash from layer name and trait path
    const input = `${layerName}:${traitPath}`.toLowerCase();
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    // Convert to positive hex string and pad
    const hexHash = Math.abs(hash).toString(16).padStart(8, '0');
    return `trait-${hexHash}`;
  }

  /**
   * Generate unique trait ID (legacy method for compatibility)
   * @deprecated Use generateStableTraitId instead
   */
  private generateTraitId(): string {
    return `trait_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Get file extension
   */
  private getFileExtension(fileName: string): string {
    const lastDot = fileName.lastIndexOf('.')
    return lastDot > 0 ? fileName.substring(lastDot).toLowerCase() : ''
  }

  /**
   * Get file name without extension
   */
  private getFileNameWithoutExtension(fileName: string): string {
    const lastDot = fileName.lastIndexOf('.')
    return lastDot > 0 ? fileName.substring(0, lastDot) : fileName
  }

  /**
   * Check if file is an image
   */
  private isImageFile(fileName: string): boolean {
    const extension = this.getFileExtension(fileName)
    return this.defaultOptions.supportedFormats.includes(extension)
  }

  /**
   * Create FileList from File array for content analysis
   */
  private createFileListFromArray(files: File[]): FileList {
    // Create a mock FileList object
    const fileList = {
      length: files.length,
      item: (index: number) => files[index] || null,
      [Symbol.iterator]: function* () {
        for (let i = 0; i < files.length; i++) {
          yield files[i]
        }
      }
    }

    // Add array-like access
    files.forEach((file, index) => {
      (fileList as any)[index] = file
    })

    return fileList as FileList
  }
}

interface DirectoryStructure {
  name: string
  type: 'file' | 'directory'
  handle: FileSystemHandle
  extension?: string
  children?: DirectoryStructure[]
}

// Export singleton instance
export const layerImportService = new LayerImportService()
