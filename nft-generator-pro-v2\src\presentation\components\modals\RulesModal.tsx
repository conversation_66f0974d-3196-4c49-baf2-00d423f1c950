import React, { useState, useEffect, useCallback } from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Divider,
  Stack,
  IconButton,
  Chip,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Card,
  CardContent,
  CardActions,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemButton,
  Paper,
  Collapse,
  Tooltip,
  Badge,
  useTheme,
  CircularProgress,
  Grid,
  Tab,
  Tabs,
  Switch
} from '@mui/material'
import {
  Close as CloseIcon,
  Rule as RuleIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Flag as FlagIcon,
  Refresh as RefreshIcon,
  Save as SaveIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Lightbulb as SuggestionIcon
} from '@mui/icons-material'
import { v4 as uuidv4 } from 'uuid'

// Import V1 compatible types and utilities
import {
  Rule,
  RuleType,
  RulePriority,
  RuleConflict,
  priorityConfig,
  RuleCondition
} from '../../../shared/types/Rules.types'
import {
  validateRule,
  detectRuleConflicts,
  sortRulesByPriority,
  getRuleSummary,
  generateRuleId,
  createDefaultRule
} from '../../../shared/utils/rulesUtils'
import { RuleFormEditor } from './RuleFormEditor'
import { useAppStore } from '../../../application/stores/appStore'
import { useContentAnalysis } from '../../contexts/ContentAnalysisContext'
import { ContentAnalysisDialog } from '../content-analysis/ContentAnalysisDialog'
import { useUnifiedMemory } from '../../../services/memory/unified-memory.service'
/**
 * Validate form data against current layers and clean invalid references
 */
function validateFormDataAgainstCurrentLayers(formData: any, layers: any[]): any {
  if (!formData || !layers.length) return formData

  // Get all valid layer IDs
  const validLayerIds = new Set(layers.map(layer => layer.id))

  // Get all valid trait IDs
  const validTraitIds = new Set()
  layers.forEach((layer: any) => {
    layer.traitGroups?.forEach((group: any) => {
      group.traits.forEach((trait: any) => {
        validTraitIds.add(trait.id)
      })
    })
  })

  // Clean form data
  const cleanedFormData = { ...formData }

  // Clean conditions if they exist
  if (cleanedFormData.conditions && Array.isArray(cleanedFormData.conditions)) {
    cleanedFormData.conditions = cleanedFormData.conditions.map((condition: any) => {
      const cleanedCondition = { ...condition }

      // Clean IF conditions
      if (cleanedCondition.ifConditions && Array.isArray(cleanedCondition.ifConditions)) {
        cleanedCondition.ifConditions = cleanedCondition.ifConditions.map((ifCond: any) => ({
          ...ifCond,
          layerId: validLayerIds.has(ifCond.layerId) ? ifCond.layerId : '',
          traitId: validTraitIds.has(ifCond.traitId) ? ifCond.traitId : ''
        }))
      }

      // Clean THEN conditions
      if (cleanedCondition.thenConditions && Array.isArray(cleanedCondition.thenConditions)) {
        cleanedCondition.thenConditions = cleanedCondition.thenConditions.map((thenCond: any) => ({
          ...thenCond,
          layerId: validLayerIds.has(thenCond.layerId) ? thenCond.layerId : '',
          traitId: validTraitIds.has(thenCond.traitId) ? thenCond.traitId : ''
        }))
      }

      return cleanedCondition
    })
  }

  return cleanedFormData
}

interface RulesModalProps {
  open: boolean
  onClose: () => void
}

// V1 Compatible Sample Rules
const sampleRules: Rule[] = [
  {
    id: '1',
    name: 'No Red Eyes with Blue Hat',
    type: 'IF_THEN',
    conditions: [
      { layerId: 'eyes', traitId: 'red_eyes' },
      { targetLayerId: 'hat', targetTraitId: 'blue_hat', operator: 'CANNOT_HAVE' }
    ],
    description: 'Prevents red eyes from appearing with blue hat - classic exclusion rule',
    order: 50,

    enabled: true
  },
  {
    id: '2',
    name: 'Face-Hair Compatibility',
    type: 'IF_THEN',
    conditions: [
      { layerId: 'face', traitId: 'round_face' },
      { targetLayerId: 'hair', targetTraitId: 'short_hair', operator: 'MUST_HAVE' }
    ],
    description: 'Round faces must have short hair for better visual harmony',
    order: 10,

    enabled: false
  },
  {
    id: '3',
    name: 'Background Layer Group',
    type: 'LAYER_GROUP',
    conditions: [],
    groupLayers: ['background', 'environment'],
    groupBehavior: 'sync',
    description: 'Groups background and environment layers together',
    order: 100,

    enabled: true
  },
  {
    id: '4',
    name: 'Complex OR Logic Example',
    type: 'IF_THEN',
    conditions: [
      { layerId: 'background', traitId: 'sunset' },
      { layerId: 'background', traitId: 'ocean', logicalOperator: 'OR' },
      { targetLayerId: 'lighting', targetTraitId: 'special_glow', operator: 'MUST_HAVE' }
    ],
    description: 'IF sunset OR ocean background THEN special lighting - demonstrates V2 OR logic',
    order: 25,

    enabled: false
  },
  {
    id: '5',
    name: 'Accessory Trait Group',
    type: 'TRAIT_GROUP',
    conditions: [],
    groupTraits: [
      { layerId: 'accessories', traitIds: ['golden_chain', 'silver_chain', 'pearl_necklace'] },
      { layerId: 'earrings', traitIds: ['diamond_studs', 'gold_hoops'] }
    ],
    groupBehavior: 'reference',
    description: 'Groups luxury accessories for coordinated generation',
    order: 75,

    enabled: true
  }
]

export const RulesModal: React.FC<RulesModalProps> = ({
  open,
  onClose
}) => {
  // Get real layers, traits and rules from app store
  const { currentProject, rules, addRule, updateRule, deleteRule, toggleRuleEnabled } = useAppStore()
  const layers = currentProject?.layers || []

  // Use unified memory for persistent modal state
  const {
    rulesModal,
    setRulesModalOpen,
    setRulesModalTab,
    setRulesModalEditingRule,
    setRulesModalFormData,
    resetRulesModal
  } = useUnifiedMemory()

  // Validate and clean form data when modal opens or layers change
  useEffect(() => {
    if (open && rulesModal.formData && layers.length > 0) {
      const validatedFormData = validateFormDataAgainstCurrentLayers(rulesModal.formData, layers)
      if (JSON.stringify(validatedFormData) !== JSON.stringify(rulesModal.formData)) {
        console.log('🔧 Cleaning invalid form data after layer changes')
        setRulesModalFormData(validatedFormData)
      }
    }
  }, [open, layers, rulesModal.formData, setRulesModalFormData])

  // Extract state from unified memory
  const { activeTab, showForm, editingRuleId, formData } = rulesModal

  // Local state for conflicts (not persisted)
  const [conflicts, setConflicts] = useState<RuleConflict[]>([])
  const [isCheckingConflicts, setIsCheckingConflicts] = useState(false)
  const [showConflicts, setShowConflicts] = useState(false)
  const [groupLayers, setGroupLayers] = useState<string[]>([])
  const [groupTraits, setGroupTraits] = useState<{layerId: string, traitIds: string[]}[]>([])

  // Content Analysis
  const {
    analysisResults,
    showAnalysisResults,
    hideAnalysisResults,
    isAnalysisDialogOpen,
    handleAcceptSuggestion,
    handleRejectSuggestion,
    handleAcceptAllSuggestions
  } = useContentAnalysis()
  const [showSuggestionDialog, setShowSuggestionDialog] = useState(false)

  const theme = useTheme()

  // Sync modal open state with unified memory
  useEffect(() => {
    setRulesModalOpen(open)
  }, [open, setRulesModalOpen])

  // Close editing/adding forms when modal closes
  useEffect(() => {
    if (!open) {
      setConflicts([])
      setShowConflicts(false)
      // Don't reset rules modal state - keep it persistent
    }
  }, [open])

  // Handle modal close - persist state but reset conflicts
  const handleClose = () => {
    setConflicts([])
    setShowConflicts(false)
    onClose()
  }

  // Conflict detection
  const detectConflicts = useCallback(async (rulesToCheck: Rule[]) => {
    setIsCheckingConflicts(true)
    try {
      // Simulate async conflict detection
      await new Promise(resolve => setTimeout(resolve, 1000))
      const detectedConflicts = detectRuleConflicts(rulesToCheck)
      setConflicts(detectedConflicts)
      if (detectedConflicts.length > 0) {
        setShowConflicts(true)
      }
    } finally {
      setIsCheckingConflicts(false)
    }
  }, [])

  // Rule management functions
  const handleDeleteRule = (ruleId: string) => {
    deleteRule(ruleId)
  }

  const handleToggleRule = (ruleId: string) => {
    toggleRuleEnabled(ruleId)
  }

  const handleEditRule = (ruleId: string) => {
    setRulesModalEditingRule(ruleId)
  }

  const handleChangePriority = (ruleId: string, priority: RulePriority) => {
    // Priority removed - no longer needed
  }

  const handleAddRule = () => {
    setRulesModalEditingRule(null) // null means new rule
  }

  const handleAddGroupRule = () => {
    setRulesModalEditingRule(null) // null means new rule
  }

  const handleSaveRule = (rule: Rule) => {
    if (editingRuleId) {
      // Update existing rule
      updateRule(editingRuleId, rule)
    } else {
      // Add new rule
      addRule(rule)
    }

    setRulesModalEditingRule(null) // Close form
    setRulesModalFormData(null) // Clear form data
  }

  const handleCancelEdit = () => {
    setRulesModalEditingRule(null) // Close form
    setRulesModalFormData(null) // Clear form data
  }

  // Suggestion handlers
  const handleShowSuggestions = () => {
    if (analysisResults) {
      setShowSuggestionDialog(true)
    }
  }

  const handleCloseSuggestions = () => {
    setShowSuggestionDialog(false)
  }

  const getRuleTypeColor = (type: RuleType) => {
    switch (type) {
      case 'IF_THEN': return 'primary'
      case 'LAYER_GROUP': return 'success'
      case 'TRAIT_GROUP': return 'warning'
      default: return 'default'
    }
  }

  const getRuleTypeLabel = (type: RuleType) => {
    switch (type) {
      case 'IF_THEN': return 'IF-THEN'
      case 'LAYER_GROUP': return 'Layer Group'
      case 'TRAIT_GROUP': return 'Trait Group'
      default: return type
    }
  }

  // Helper functions for rule conflicts
  const getConflictingRuleCount = () => {
    const uniqueRuleIds = new Set<string>()
    conflicts.forEach(conflict => {
      conflict.ruleIds.forEach(id => uniqueRuleIds.add(id))
    })
    return uniqueRuleIds.size
  }

  const getRuleConflicts = (ruleId: string) => {
    return conflicts.filter(conflict => conflict.ruleIds.includes(ruleId))
  }

  const hasErrorConflicts = (ruleId: string) => {
    return conflicts.some(conflict =>
      conflict.severity === 'error' && conflict.ruleIds.includes(ruleId)
    )
  }

  const hasWarningConflicts = (ruleId: string) => {
    return conflicts.some(conflict =>
      conflict.severity === 'warning' && conflict.ruleIds.includes(ruleId)
    )
  }

  // Get sorted rules for the current tab
  const getSortedRulesForTab = () => {
    if (!rules || !Array.isArray(rules)) {
      return []
    }

    if (activeTab === 'trait') {
      return rules
        .filter(rule => rule.type === 'IF_THEN')
        .sort((a, b) => (a.order || 0) - (b.order || 0))
    } else if (activeTab === 'layer-group') {
      return rules
        .filter(rule => rule.type === 'LAYER_GROUP')
        .sort((a, b) => (a.order || 0) - (b.order || 0))
    } else if (activeTab === 'trait-group') {
      return rules
        .filter(rule => rule.type === 'TRAIT_GROUP')
        .sort((a, b) => (a.order || 0) - (b.order || 0))
    }
    return []
  }

  // Real layer and trait name functions using NFT context data
  const getLayerName = (layerId: string | null | undefined): string => {
    if (!layerId) return 'Unknown Layer'

    // Check if layers are available
    if (!layers || !Array.isArray(layers)) {
      console.warn('getLayerName: layers is not available or not an array')
      return layerId.charAt(0).toUpperCase() + layerId.slice(1)
    }

    const layer = layers.find(l => l.id === layerId)
    if (!layer) {
      console.warn(`getLayerName: layer with ID ${layerId} not found`)
      return layerId.charAt(0).toUpperCase() + layerId.slice(1)
    }

    return layer.name
  }

  const getTraitName = (layerId: string | null | undefined, traitId: string | null | undefined): string => {
    if (!traitId) return 'Unknown Trait'
    if (!layerId) return traitId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())

    // Check if layers are available
    if (!layers || !Array.isArray(layers)) {
      console.warn('getTraitName: layers is not available or not an array')
      return traitId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }

    const layer = layers.find(l => l.id === layerId)
    if (!layer) {
      console.warn(`getTraitName: layer with ID ${layerId} not found`)
      return traitId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }

    if (!layer.traits || !Array.isArray(layer.traits)) {
      console.warn(`getTraitName: traits for layer ${layer.name} (${layerId}) are not available or not an array`)
      return traitId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }

    const trait = layer.traits.find(t => t.id === traitId)
    if (!trait) {
      console.warn(`getTraitName: trait with ID ${traitId} not found in layer ${layer.name}`)
      return traitId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }

    return trait.name
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          width: '80%',
          height: '80%',
          maxWidth: 'unset',
          maxHeight: 'unset',
          display: 'flex',
          flexDirection: 'column',
        }
      }}
    >
      <DialogTitle sx={{ p: 1.5, pb: 1.5, m: 0 }}>
        Compatibility Rules
        <IconButton
          onClick={onClose}
          sx={{ position: 'absolute', right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      {/* Conflict Detection Banner */}
      {conflicts.length > 0 && (
        <Box
          sx={{
            px: 1.5,
            py: 1,
            backgroundColor: conflicts.some(c => c.severity === 'error')
              ? theme.palette.error.light
              : theme.palette.warning.light,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {conflicts.some(c => c.severity === 'error') ? (
              <ErrorIcon color="error" fontSize="small" />
            ) : (
              <WarningIcon color="warning" fontSize="small" />
            )}
            <Typography variant="body2">
              {conflicts.some(c => c.severity === 'error')
                ? `${getConflictingRuleCount()} rules have conflicts that must be resolved`
                : `${getConflictingRuleCount()} rules have potential conflicts that should be reviewed`}
            </Typography>
          </Box>
          <Button
            size="small"
            variant="outlined"
            color={conflicts.some(c => c.severity === 'error') ? "error" : "warning"}
            onClick={() => setShowConflicts(!showConflicts)}
          >
            {showConflicts ? "Hide Details" : "Show Details"}
          </Button>
        </Box>
      )}

      {/* Conflict Details */}
      <Collapse in={showConflicts && conflicts.length > 0}>
        <Box sx={{ px: 1.5, py: 1, backgroundColor: 'background.paper', borderTop: `1px solid ${theme.palette.divider}` }}>
          <Typography variant="subtitle2" gutterBottom>
            Rule Conflicts
          </Typography>
          <List dense sx={{ maxHeight: '200px', overflow: 'auto' }}>
            {conflicts.map((conflict, index) => (
              <ListItem key={`conflict-${index}`}>
                <ListItemIcon sx={{ minWidth: 36 }}>
                  {conflict.severity === 'error' ? (
                    <ErrorIcon color="error" fontSize="small" />
                  ) : (
                    <WarningIcon color="warning" fontSize="small" />
                  )}
                </ListItemIcon>
                <ListItemText
                  primary={conflict.message}
                  secondary={
                    <Box sx={{ mt: 0.5 }}>
                      <Typography variant="caption" component="div">
                        Affected Rules:
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                        {conflict.ruleIds.map(id => {
                          const rule = (rules || []).find(r => r.id === id)
                          return rule ? (
                            <Chip
                              key={id}
                              label={rule.name}
                              size="small"
                              color={conflict.severity === 'error' ? "error" : "warning"}
                              variant="outlined"
                              onClick={() => handleEditRule(id)}
                              sx={{ cursor: 'pointer' }}
                            />
                          ) : null
                        })}
                      </Box>
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        </Box>
      </Collapse>

      <DialogContent dividers sx={{ p: 0, display: 'flex', flexDirection: 'column', flexGrow: 1, overflow: 'hidden' }}>
        {/* Main content wrapper - Flex layout */}
        <Box sx={{ display: 'flex', flexGrow: 1, overflow: 'hidden' }}>
          {/* Sidebar - fixed width */}
          <Box sx={{
            width: 300,
            minWidth: 300,
            maxWidth: 300,
            borderRight: `1px solid ${theme.palette.divider}`,
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
          }}>
            {/* Sidebar Header with Tabs */}
            <Box sx={{ p: 1.5, borderBottom: `1px solid ${theme.palette.divider}` }}>
              <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
                <Box sx={{ display: 'flex' }}>
                  <Box
                    sx={{
                      px: 2,
                      py: 1,
                      cursor: 'pointer',
                      borderBottom: '2px solid',
                      borderColor: !showForm && activeTab === 'trait' ? 'primary.main' : 'transparent',
                      color: !showForm && activeTab === 'trait' ? 'primary.main' : 'text.primary',
                      fontWeight: !showForm && activeTab === 'trait' ? 'medium' : 'normal',
                    }}
                    onClick={() => {
                      if (!showForm) {
                        setRulesModalTab('trait')
                      }
                    }}
                  >
                    Trait Rules
                  </Box>
                  <Box
                    sx={{
                      px: 2,
                      py: 1,
                      cursor: 'pointer',
                      borderBottom: '2px solid',
                      borderColor: !showForm && activeTab === 'layer-group' ? 'primary.main' : 'transparent',
                      color: !showForm && activeTab === 'layer-group' ? 'primary.main' : 'text.primary',
                      fontWeight: !showForm && activeTab === 'layer-group' ? 'medium' : 'normal',
                    }}
                    onClick={() => {
                      if (!showForm) {
                        setRulesModalTab('layer-group')
                      }
                    }}
                  >
                    Layer Grouping
                  </Box>
                  <Box
                    sx={{
                      px: 2,
                      py: 1,
                      cursor: 'pointer',
                      borderBottom: '2px solid',
                      borderColor: !showForm && activeTab === 'trait-group' ? 'primary.main' : 'transparent',
                      color: !showForm && activeTab === 'trait-group' ? 'primary.main' : 'text.primary',
                      fontWeight: !showForm && activeTab === 'trait-group' ? 'medium' : 'normal',
                    }}
                    onClick={() => {
                      if (!showForm) {
                        setRulesModalTab('trait-group')
                      }
                    }}
                  >
                    Trait Grouping
                  </Box>
                </Box>
              </Box>

              <Typography variant="subtitle1" gutterBottom>
                {activeTab === 'trait' ? 'Trait Rules List' : 'Group Rules List'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {activeTab === 'trait'
                  ? 'Define rules to control trait combinations.'
                  : 'Group layers to treat them as a single layer for dice rolls.'}
              </Typography>

              {/* Rule Conflicts Check Button */}
              <Box sx={{ mt: 1.5, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Tooltip title="Check for rule conflicts">
                  <Button
                    size="small"
                    variant="outlined"
                    color="primary"
                    startIcon={isCheckingConflicts ? <CircularProgress size={16} /> : <RefreshIcon />}
                    onClick={() => detectConflicts(rules || [])}
                    disabled={isCheckingConflicts}
                  >
                    Check Conflicts
                  </Button>
                </Tooltip>

                <Box>
                  <Typography variant="caption" color="text.secondary">
                    {getSortedRulesForTab().length} rules
                  </Typography>
                </Box>
              </Box>
            </Box>

            {/* Add Rule Button */}
            <Box sx={{ p: 1.5, borderTop: `1px solid ${theme.palette.divider}` }}>
              <Button
                variant="contained"
                fullWidth
                size="medium"
                startIcon={<AddIcon />}
                onClick={activeTab === 'trait' ? handleAddRule : handleAddGroupRule}
                disabled={showForm}
              >
                {activeTab === 'trait'
                  ? 'Add Trait Rule'
                  : activeTab === 'layer-group'
                    ? 'Add Layer Group Rule'
                    : 'Add Trait Group Rule'}
              </Button>
            </Box>

            {/* Rules List */}
            <Box sx={{ overflowY: 'auto', flexGrow: 1 }}>
              <List>
                {getSortedRulesForTab().map((rule) => {
                  // Get rule conflicts
                  getRuleConflicts(rule.id)
                  const hasError = hasErrorConflicts(rule.id)
                  const hasWarning = hasWarningConflicts(rule.id)

                  return (
                    <ListItem
                      key={rule.id}
                      disablePadding
                      secondaryAction={
                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                          <Tooltip title="Change priority">
                            <IconButton
                              edge="end"
                              onClick={(e) => {
                                e.stopPropagation()

                                // Priority removed - no action needed
                              }}
                              size="small"
                              color="primary"
                            >
                              <FlagIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>

                          <Tooltip title="Delete rule">
                            <IconButton
                              edge="end"
                              onClick={(e) => {
                                e.stopPropagation()
                                handleDeleteRule(rule.id)
                              }}
                              size="small"
                              color="error"
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      }
                      sx={{
                        opacity: rule.enabled === false ? 0.5 : 1,
                        backgroundColor: hasError
                          ? theme.palette.error.light
                          : hasWarning
                            ? theme.palette.warning.light
                            : 'transparent'
                      }}
                    >
                      <ListItemButton
                        onClick={() => handleEditRule(rule.id)}
                        selected={editingRuleId === rule.id}
                        sx={{ pr: 6 }}
                      >
                        <ListItemIcon>
                          <Box sx={{ position: 'relative' }}>
                            <RuleIcon color={rule.enabled === false ? "disabled" : "primary"} />
                            {(hasError || hasWarning) && (
                              <Box
                                sx={{
                                  position: 'absolute',
                                  top: -5,
                                  right: -5,
                                  width: 12,
                                  height: 12,
                                  borderRadius: '50%',
                                  backgroundColor: hasError ? 'error.main' : 'warning.main',
                                  border: `1px solid ${theme.palette.background.paper}`
                                }}
                              />
                            )}
                          </Box>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, justifyContent: 'space-between', width: '100%' }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flex: 1 }}>
                                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                  {rule.name}
                                </Typography>
                                {rule.description && (
                                  <Typography variant="caption" color="text.secondary">
                                    - {rule.description}
                                  </Typography>
                                )}
                              </Box>

                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Switch
                                  checked={rule.enabled !== false}
                                  onChange={() => toggleRuleEnabled(rule.id)}
                                  size="small"
                                  color="primary"
                                />
                                {rule.enabled === false && (
                                  <Chip
                                    label="Disabled"
                                    size="small"
                                    color="default"
                                    variant="outlined"
                                    sx={{ height: 18, '& .MuiChip-label': { px: 0.8, py: 0 } }}
                                  />
                                )}
                              </Box>
                            </Box>
                          }
                          secondary={
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                                maxWidth: '180px'
                              }}
                            >
                              {getRuleSummary(rule)}
                            </Typography>
                          }
                        />
                      </ListItemButton>
                    </ListItem>
                  )
                })}
              </List>
            </Box>
          </Box>

          {/* Main Content Area - Rule Editor */}
          <Box sx={{ flexGrow: 1, p: 2, overflow: 'auto' }}>
            {!showForm ? (
              // Welcome/Info Screen
              <Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                textAlign: 'center'
              }}>
                <RuleIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h5" gutterBottom>
                  Advanced Rule Engine V2
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ mb: 3, maxWidth: 400 }}>
                  Create sophisticated rules to control trait combinations, layer grouping, and generation logic.
                  Select a rule from the sidebar to edit, or create a new rule.
                </Typography>

                <Alert severity="info" sx={{ mb: 3, maxWidth: 500 }}>
                  <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                    V2 Enhancements:
                  </Typography>
                  <Box component="ul" sx={{ mt: 1, mb: 0, pl: 2, textAlign: 'left' }}>
                    <li>Complex IF-THEN logic with OR/AND operators</li>
                    <li>Layer grouping for synchronized generation</li>
                    <li>Trait grouping for coordinated combinations</li>
                    <li>Priority-based rule execution</li>
                    <li>Real-time conflict detection</li>
                  </Box>
                </Alert>

                <Stack direction="row" spacing={2}>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={handleAddRule}
                  >
                    Create New Rule
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={() => detectConflicts(rules)}
                    disabled={isCheckingConflicts}
                  >
                    Check All Conflicts
                  </Button>
                </Stack>

                {/* Rule Statistics */}
                {rules.length > 0 && (
                  <Box sx={{ mt: 4, p: 2, bgcolor: 'background.default', borderRadius: 1, minWidth: 400 }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 2 }}>
                      Rule Statistics
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 2 }}>
                      <Box sx={{ flex: 1, textAlign: 'center' }}>
                        <Typography variant="h4" color="primary.main">{rules.length}</Typography>
                        <Typography variant="caption" color="text.secondary">Total Rules</Typography>
                      </Box>
                      <Box sx={{ flex: 1, textAlign: 'center' }}>
                        <Typography variant="h4" color="success.main">
                          {rules.filter(r => r.enabled).length}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">Active Rules</Typography>
                      </Box>
                      <Box sx={{ flex: 1, textAlign: 'center' }}>
                        <Typography variant="h4" color="warning.main">
                          {conflicts.length}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">Conflicts</Typography>
                      </Box>
                    </Box>
                  </Box>
                )}
              </Box>
            ) : (
              // Rule Editor Form
              <RuleFormEditor
                rule={editingRuleId ? rules.find(r => r.id === editingRuleId) : null}
                ruleType={activeTab === 'trait' ? 'IF_THEN' : activeTab === 'layer-group' ? 'LAYER_GROUP' : 'TRAIT_GROUP'}
                onSave={handleSaveRule}
                onCancel={handleCancelEdit}
              />
            )}
          </Box>
        </Box>
      </DialogContent>

      {/* Actions */}
      <DialogActions sx={{ px: 3, pb: 3, flexShrink: 0, justifyContent: 'space-between' }}>
        {/* Left side - Suggestion button */}
        <Box>
          {analysisResults && (
            <Button
              startIcon={<SuggestionIcon />}
              onClick={handleShowSuggestions}
              color="secondary"
              variant="outlined"
              size="small"
            >
              Rule Suggestions ({analysisResults.suggestions.length})
            </Button>
          )}
        </Box>

        {/* Right side - Main actions */}
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button onClick={onClose} color="inherit">
            Close
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              const activeRules = rules.filter(r => r.enabled)
              console.log('Applying rules:', activeRules)

              // Show success message
              alert(`✅ Applied ${activeRules.length} rules successfully!\n\nActive rules:\n${activeRules.map(r => `• ${r.name}`).join('\n')}\n\nThese rules will be enforced during NFT generation.`)

              // TODO: Implement actual rule application logic
              // This would integrate with the generation engine
              onClose()
            }}
            disabled={showForm}
          >
            Apply Rules ({rules.filter(r => r.enabled).length})
          </Button>
        </Box>
      </DialogActions>

      {/* Content Analysis Dialog */}
      {showSuggestionDialog && analysisResults && (
        <ContentAnalysisDialog
          open={showSuggestionDialog}
          onClose={handleCloseSuggestions}
          analysisResults={analysisResults}
          onAcceptSuggestion={handleAcceptSuggestion}
          onRejectSuggestion={handleRejectSuggestion}
          onAcceptAllSuggestions={handleAcceptAllSuggestions}
        />
      )}
    </Dialog>
  )
}
