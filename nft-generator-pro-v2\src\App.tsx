import React, { useEffect } from 'react'
import { CssBaseline } from '@mui/material'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Toaster } from 'react-hot-toast'
import { ThemeProvider } from './presentation/theme/ThemeProvider'
import { ErrorBoundary } from './presentation/components/common/ErrorBoundary'
import { AppLayout } from './presentation/components/common/AppLayout'
import { HomePage } from './presentation/pages/HomePage'
import { WorkspacePage } from './presentation/pages/WorkspacePage'
import { useAppStore } from './application/stores/appStore'
import { ContentAnalysisProvider } from './presentation/contexts/ContentAnalysisContext'
import { initializeUnifiedMemory } from './services/memory/unified-memory.service'
import { initializeImagePersistence } from './services/memory/image-persistence.service'

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (renamed from cacheTime in v5)
    },
  },
})

function App() {
  const currentProject = useAppStore((state) => state.currentProject)

  // Initialize unified memory system on app start
  useEffect(() => {
    const initializeMemorySystem = async () => {
      try {
        console.log('🚀 Initializing memory system...')

        // Initialize unified memory
        await initializeUnifiedMemory()
        console.log('✅ Unified memory system initialized')

        // Initialize image persistence
        await initializeImagePersistence()
        console.log('✅ Image persistence system initialized')

        console.log('✅ Memory system placeholder initialized')

      } catch (error) {
        console.error('❌ Failed to initialize memory system:', error)
      }
    }

    initializeMemorySystem()
  }, [])

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <CssBaseline />
        <ErrorBoundary>
          <ContentAnalysisProvider>
            <AppLayout>
              {currentProject ? (
                <WorkspacePage />
              ) : (
                <HomePage />
              )}
            </AppLayout>
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#363636',
                  color: '#fff',
                },
              }}
            />
          </ContentAnalysisProvider>
        </ErrorBoundary>
      </ThemeProvider>
    </QueryClientProvider>
  )
}

export default App
